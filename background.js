// تعريف الأوقات الافتراضية للأذكار
const DEFAULT_TIMES = {
  morning: "05:00", // وقت أذكار الصباح
  evening: "17:00", // وقت أذكار المساء
  afterPrayer: true // تفعيل أذكار بعد الصلاة
};

// تهيئة الإعدادات عند تثبيت الإضافة
chrome.runtime.onInstalled.addListener(() => {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    if (!result.azkarTimes) {
      chrome.storage.local.set({ azkarTimes: DEFAULT_TIMES });
    }
    setupAlarms();
  });
});

// إعداد المنبهات بناءً على الأوقات المحددة
function setupAlarms() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || DEFAULT_TIMES;
    
    // إزالة المنبهات القديمة
    chrome.alarms.clearAll();
    
    // إعداد منبه الصباح
    const [morningHours, morningMinutes] = times.morning.split(':').map(Number);
    const morningTime = new Date();
    morningTime.setHours(morningHours, morningMinutes, 0);
    if (morningTime < new Date()) {
      morningTime.setDate(morningTime.getDate() + 1);
    }
    
    chrome.alarms.create('morningAzkar', {
      when: morningTime.getTime(),
      periodInMinutes: 24 * 60 // تكرار كل 24 ساعة
    });
    
    // إعداد منبه المساء
    const [eveningHours, eveningMinutes] = times.evening.split(':').map(Number);
    const eveningTime = new Date();
    eveningTime.setHours(eveningHours, eveningMinutes, 0);
    if (eveningTime < new Date()) {
      eveningTime.setDate(eveningTime.getDate() + 1);
    }
    
    chrome.alarms.create('eveningAzkar', {
      when: eveningTime.getTime(),
      periodInMinutes: 24 * 60 // تكرار كل 24 ساعة
    });
  });
}

// الاستماع لتغييرات الإعدادات
chrome.storage.onChanged.addListener((changes) => {
  if (changes.azkarTimes) {
    setupAlarms();
  }
});

// معالجة المنبهات
chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === 'morningAzkar') {
    showNotification('أذكار الصباح', 'حان وقت أذكار الصباح');
  } else if (alarm.name === 'eveningAzkar') {
    showNotification('أذكار المساء', 'حان وقت أذكار المساء');
  }
});

// عرض الإشعارات
function showNotification(title, message) {
  chrome.notifications.create({
    type: 'basic',
    iconUrl: 'images/icon128.png',
    title: title,
    message: message,
    priority: 2
  });
}
