// تعريف الأوقات الافتراضية للأذكار
const DEFAULT_TIMES = {
  morning: "05:00", // وقت أذكار الصباح
  evening: "17:00", // وقت أذكار المساء
  afterPrayer: true // تفعيل أذكار بعد الصلاة
};

// تهيئة الإعدادات عند تثبيت الإضافة
chrome.runtime.onInstalled.addListener(() => {
  try {
    chrome.storage.local.get(['azkarTimes'], (result) => {
      if (chrome.runtime.lastError) {
        console.error('خطأ في قراءة الإعدادات:', chrome.runtime.lastError);
        return;
      }

      if (!result.azkarTimes) {
        chrome.storage.local.set({ azkarTimes: DEFAULT_TIMES }, () => {
          if (chrome.runtime.lastError) {
            console.error('خطأ في حفظ الإعدادات الافتراضية:', chrome.runtime.lastError);
          } else {
            console.log('تم تعيين الإعدادات الافتراضية بنجاح');
          }
        });
      }
      setupAlarms();
    });
  } catch (error) {
    console.error('خطأ في تهيئة الإضافة:', error);
  }
});

// إعداد المنبهات بناءً على الأوقات المحددة
function setupAlarms() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || DEFAULT_TIMES;
    
    // إزالة المنبهات القديمة
    chrome.alarms.clearAll();
    
    // إعداد منبه الصباح
    const [morningHours, morningMinutes] = times.morning.split(':').map(Number);
    const morningTime = new Date();
    morningTime.setHours(morningHours, morningMinutes, 0);
    if (morningTime < new Date()) {
      morningTime.setDate(morningTime.getDate() + 1);
    }
    
    chrome.alarms.create('morningAzkar', {
      when: morningTime.getTime(),
      periodInMinutes: 24 * 60 // تكرار كل 24 ساعة
    });
    
    // إعداد منبه المساء
    const [eveningHours, eveningMinutes] = times.evening.split(':').map(Number);
    const eveningTime = new Date();
    eveningTime.setHours(eveningHours, eveningMinutes, 0);
    if (eveningTime < new Date()) {
      eveningTime.setDate(eveningTime.getDate() + 1);
    }
    
    chrome.alarms.create('eveningAzkar', {
      when: eveningTime.getTime(),
      periodInMinutes: 24 * 60 // تكرار كل 24 ساعة
    });
  });
}

// الاستماع لتغييرات الإعدادات
chrome.storage.onChanged.addListener((changes) => {
  if (changes.azkarTimes) {
    setupAlarms();
  }
});

// معالجة المنبهات
chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === 'morningAzkar') {
    showNotification('أذكار الصباح', 'حان وقت أذكار الصباح');
  } else if (alarm.name === 'eveningAzkar') {
    showNotification('أذكار المساء', 'حان وقت أذكار المساء');
  }
});

// عرض الإشعارات
function showNotification(title, message) {
  chrome.notifications.create({
    type: 'basic',
    iconUrl: 'images/icon128.png',
    title: title,
    message: message,
    priority: 2,
    buttons: [
      { title: 'عرض الأذكار' },
      { title: 'إغلاق' }
    ]
  }, (notificationId) => {
    if (chrome.runtime.lastError) {
      console.error('خطأ في إنشاء الإشعار:', chrome.runtime.lastError);
    }
  });
}

// معالجة النقر على الإشعارات
chrome.notifications.onButtonClicked.addListener((notificationId, buttonIndex) => {
  if (buttonIndex === 0) {
    // فتح النافذة المنبثقة
    chrome.action.openPopup();
  }
  chrome.notifications.clear(notificationId);
});

// معالجة النقر على الإشعار نفسه
chrome.notifications.onClicked.addListener((notificationId) => {
  chrome.action.openPopup();
  chrome.notifications.clear(notificationId);
});
