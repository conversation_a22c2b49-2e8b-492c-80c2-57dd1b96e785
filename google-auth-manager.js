// مدير تسجيل الدخول بـ Google
class GoogleAuthManager {
  constructor() {
    this.isSignedIn = false;
    this.userInfo = null;
    this.accessToken = null;
  }

  // تهيئة مدير Google Auth
  async init() {
    try {
      // التحقق من حالة تسجيل الدخول المحفوظة
      const result = await chrome.storage.sync.get(['googleAuth']);
      if (result.googleAuth) {
        this.userInfo = result.googleAuth.userInfo;
        this.isSignedIn = true;
        this.updateUI();
      }
    } catch (error) {
      console.error('خطأ في تهيئة Google Auth:', error);
    }
  }

  // تسجيل الدخول بـ Google
  async signIn() {
    try {
      // التحقق من توفر Chrome Identity API
      if (!chrome.identity) {
        this.showMessage('ميزة تسجيل الدخول بـ Google غير متاحة في هذا المتصفح', 'error');
        return false;
      }

      // التحقق من إعدادات OAuth2
      if (!chrome.runtime.getManifest().oauth2) {
        this.showMessage('إعدادات Google OAuth2 غير مكتملة. يرجى مراجعة المطور.', 'error');
        this.showSetupInstructions();
        return false;
      }

      this.showMessage('جاري تسجيل الدخول...', 'info');

      // طلب الحصول على رمز الوصول
      const token = await chrome.identity.getAuthToken({
        interactive: true
      });

      if (token) {
        this.accessToken = token;

        // الحصول على معلومات المستخدم
        const userInfo = await this.getUserInfo(token);

        if (userInfo) {
          this.userInfo = userInfo;
          this.isSignedIn = true;

          // حفظ معلومات المستخدم
          await this.saveAuthData();

          // تحديث الواجهة
          this.updateUI();

          // عرض رسالة نجاح
          this.showMessage('تم تسجيل الدخول بنجاح!', 'success');

          return true;
        } else {
          this.showMessage('فشل في الحصول على معلومات المستخدم', 'error');
          return false;
        }
      } else {
        this.showMessage('فشل في الحصول على رمز الوصول', 'error');
        return false;
      }
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);

      // معالجة أنواع مختلفة من الأخطاء
      if (error.message && error.message.includes('OAuth2')) {
        this.showMessage('خطأ في إعدادات OAuth2. يرجى مراجعة إعدادات Google Cloud Console.', 'error');
        this.showSetupInstructions();
      } else if (error.message && error.message.includes('User did not approve')) {
        this.showMessage('تم إلغاء تسجيل الدخول من قبل المستخدم', 'info');
      } else {
        this.showMessage('فشل في تسجيل الدخول. يرجى المحاولة مرة أخرى.', 'error');
      }

      return false;
    }
  }

  // تسجيل الخروج
  async signOut() {
    try {
      if (this.accessToken) {
        // إلغاء رمز الوصول
        await chrome.identity.removeCachedAuthToken({ 
          token: this.accessToken 
        });
      }
      
      // مسح البيانات المحفوظة
      await chrome.storage.sync.remove(['googleAuth']);
      
      // إعادة تعيين الحالة
      this.isSignedIn = false;
      this.userInfo = null;
      this.accessToken = null;
      
      // تحديث الواجهة
      this.updateUI();
      
      this.showMessage('تم تسجيل الخروج بنجاح', 'success');
      
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      this.showMessage('حدث خطأ أثناء تسجيل الخروج', 'error');
    }
  }

  // الحصول على معلومات المستخدم من Google API
  async getUserInfo(token) {
    try {
      const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('خطأ في الحصول على معلومات المستخدم:', error);
    }
    return null;
  }

  // حفظ بيانات المصادقة
  async saveAuthData() {
    try {
      await chrome.storage.sync.set({
        googleAuth: {
          userInfo: this.userInfo,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      console.error('خطأ في حفظ بيانات المصادقة:', error);
    }
  }

  // مزامنة البيانات مع Google Drive
  async syncData() {
    if (!this.isSignedIn || !this.accessToken) {
      this.showMessage('يجب تسجيل الدخول أولاً', 'error');
      return false;
    }

    try {
      this.showMessage('جاري مزامنة البيانات...', 'info');
      
      // الحصول على جميع البيانات المحلية
      const localData = await chrome.storage.sync.get(null);
      
      // إنشاء ملف النسخ الاحتياطي
      const backupData = {
        timestamp: Date.now(),
        version: '1.0',
        data: localData
      };
      
      // رفع البيانات إلى Google Drive
      const success = await this.uploadToGoogleDrive(backupData);
      
      if (success) {
        this.showMessage('تم مزامنة البيانات بنجاح!', 'success');
        return true;
      } else {
        this.showMessage('فشل في مزامنة البيانات', 'error');
        return false;
      }
      
    } catch (error) {
      console.error('خطأ في مزامنة البيانات:', error);
      this.showMessage('حدث خطأ أثناء المزامنة', 'error');
      return false;
    }
  }

  // رفع البيانات إلى Google Drive
  async uploadToGoogleDrive(data) {
    try {
      const fileName = `azkar-backup-${Date.now()}.json`;
      const fileContent = JSON.stringify(data, null, 2);
      
      // إنشاء metadata للملف
      const metadata = {
        name: fileName,
        parents: ['appDataFolder']
      };
      
      // رفع الملف
      const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'multipart/related; boundary="foo_bar_baz"'
        },
        body: this.createMultipartBody(metadata, fileContent)
      });
      
      return response.ok;
      
    } catch (error) {
      console.error('خطأ في رفع البيانات:', error);
      return false;
    }
  }

  // إنشاء محتوى multipart للرفع
  createMultipartBody(metadata, data) {
    const delimiter = 'foo_bar_baz';
    const close_delim = `\r\n--${delimiter}--`;
    
    let body = `--${delimiter}\r\n`;
    body += 'Content-Type: application/json\r\n\r\n';
    body += JSON.stringify(metadata) + '\r\n';
    body += `--${delimiter}\r\n`;
    body += 'Content-Type: application/json\r\n\r\n';
    body += data;
    body += close_delim;
    
    return body;
  }

  // تحديث واجهة المستخدم
  updateUI() {
    const googleLogin = document.getElementById('google-login');
    const googleProfile = document.getElementById('google-profile');
    const googleUserName = document.getElementById('google-user-name');
    const googleUserEmail = document.getElementById('google-user-email');
    const googleUserPhoto = document.getElementById('google-user-photo');
    
    if (this.isSignedIn && this.userInfo) {
      // إخفاء قسم تسجيل الدخول
      if (googleLogin) googleLogin.style.display = 'none';
      
      // عرض معلومات المستخدم
      if (googleProfile) googleProfile.style.display = 'block';
      if (googleUserName) googleUserName.textContent = this.userInfo.name || 'مستخدم Google';
      if (googleUserEmail) googleUserEmail.textContent = this.userInfo.email || '';
      if (googleUserPhoto && this.userInfo.picture) {
        googleUserPhoto.src = this.userInfo.picture;
        googleUserPhoto.style.display = 'block';
      }
      
    } else {
      // عرض قسم تسجيل الدخول
      if (googleLogin) googleLogin.style.display = 'block';
      if (googleProfile) googleProfile.style.display = 'none';
    }
  }

  // عرض رسالة للمستخدم
  showMessage(message, type = 'info') {
    // استخدام نظام الرسائل الموجود في التطبيق
    if (typeof showSuccessMessage === 'function' && type === 'success') {
      showSuccessMessage(message);
    } else if (typeof showErrorMessage === 'function' && type === 'error') {
      showErrorMessage(message);
    } else {
      console.log(`${type}: ${message}`);
    }
  }

  // الحصول على حالة تسجيل الدخول
  isUserSignedIn() {
    return this.isSignedIn;
  }

  // الحصول على معلومات المستخدم
  getUserData() {
    return this.userInfo;
  }

  // عرض تعليمات الإعداد
  showSetupInstructions() {
    const instructions = document.createElement('div');
    instructions.className = 'google-setup-instructions';
    instructions.innerHTML = `
      <div class="setup-modal">
        <div class="setup-content">
          <h3>🔧 إعداد تسجيل الدخول بـ Google</h3>
          <p>لاستخدام ميزة تسجيل الدخول بـ Google، يجب إعداد المشروع في Google Cloud Console:</p>

          <ol class="setup-steps">
            <li>انتقل إلى <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
            <li>أنشئ مشروع جديد أو اختر مشروع موجود</li>
            <li>فعّل Google+ API و Google Drive API</li>
            <li>أنشئ OAuth 2.0 Client ID</li>
            <li>أضف Extension ID إلى Authorized JavaScript origins</li>
            <li>انسخ Client ID وضعه في manifest.json</li>
          </ol>

          <div class="setup-note">
            <p><strong>ملاحظة:</strong> هذه الميزة تتطلب إعداد من المطور. يمكنك استخدام الإضافة بدون تسجيل الدخول بـ Google.</p>
          </div>

          <button class="close-setup-btn" onclick="this.parentElement.parentElement.remove()">إغلاق</button>
        </div>
      </div>
    `;

    document.body.appendChild(instructions);

    // إزالة التعليمات بعد 30 ثانية
    setTimeout(() => {
      if (instructions.parentNode) {
        instructions.parentNode.removeChild(instructions);
      }
    }, 30000);
  }
}

// إنشاء مثيل عام
window.googleAuthManager = new GoogleAuthManager();
