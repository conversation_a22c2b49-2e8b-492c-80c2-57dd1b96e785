document.addEventListener('DOMContentLoaded', function() {
  // تحميل الإعدادات
  loadSettings();
  
  // عرض الوقت الحالي
  updateCurrentTime();
  setInterval(updateCurrentTime, 1000);
  
  // عرض معلومات الذكر القادم
  updateNextAzkar();
  
  // تحميل الأذكار
  loadAzkar();
  
  // إعداد التبويبات
  setupTabs();
  
  // إعداد زر حفظ الإعدادات
  document.getElementById('save-settings').addEventListener('click', saveSettings);
});

// تحديث الوقت الحالي
function updateCurrentTime() {
  const now = new Date();
  const timeString = now.toLocaleTimeString('ar-SA');
  document.getElementById('current-time').textContent = timeString;
}

// التحقق من صحة الوقت
function validateTime(timeString) {
  if (!timeString || typeof timeString !== 'string') {
    return false;
  }

  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  if (!timeRegex.test(timeString)) {
    return false;
  }

  const [hours, minutes] = timeString.split(':').map(Number);
  return hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59;
}

// تحميل الإعدادات
function loadSettings() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || {
      morning: "05:00",
      evening: "17:00",
      afterPrayer: true
    };
    
    document.getElementById('morning-time').value = times.morning;
    document.getElementById('evening-time').value = times.evening;
    document.getElementById('after-prayer').checked = times.afterPrayer;
  });
}

// حفظ الإعدادات
function saveSettings() {
  try {
    const morningTimeElement = document.getElementById('morning-time');
    const eveningTimeElement = document.getElementById('evening-time');
    const afterPrayerElement = document.getElementById('after-prayer');

    if (!morningTimeElement || !eveningTimeElement || !afterPrayerElement) {
      alert('خطأ: لا يمكن العثور على عناصر الإعدادات');
      return;
    }

    const morningTime = morningTimeElement.value;
    const eveningTime = eveningTimeElement.value;
    const afterPrayer = afterPrayerElement.checked;

    // التحقق من صحة الأوقات
    if (!morningTime || !eveningTime) {
      alert('يرجى تحديد أوقات صحيحة للأذكار');
      return;
    }

    // التحقق من صحة تنسيق الوقت
    if (!validateTime(morningTime) || !validateTime(eveningTime)) {
      alert('يرجى إدخال أوقات صحيحة (00:00 - 23:59)');
      return;
    }

    // التحقق من أن وقت المساء مختلف عن وقت الصباح
    if (morningTime === eveningTime) {
      alert('يجب أن يكون وقت أذكار المساء مختلفاً عن وقت أذكار الصباح');
      return;
    }

    const settings = {
      morning: morningTime,
      evening: eveningTime,
      afterPrayer: afterPrayer
    };

    chrome.storage.local.set({ azkarTimes: settings }, () => {
      if (chrome.runtime.lastError) {
        console.error('خطأ في حفظ الإعدادات:', chrome.runtime.lastError);
        alert('حدث خطأ في حفظ الإعدادات. يرجى المحاولة مرة أخرى.');
      } else {
        alert('تم حفظ الإعدادات بنجاح');
        updateNextAzkar();
      }
    });
  } catch (error) {
    console.error('خطأ في حفظ الإعدادات:', error);
    alert('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
  }
}

// تحديث معلومات الذكر القادم
function updateNextAzkar() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || {
      morning: "05:00",
      evening: "17:00"
    };
    
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    const [morningHours, morningMinutes] = times.morning.split(':').map(Number);
    const [eveningHours, eveningMinutes] = times.evening.split(':').map(Number);
    
    let morningTime = new Date();
    morningTime.setHours(morningHours, morningMinutes, 0);
    
    let eveningTime = new Date();
    eveningTime.setHours(eveningHours, eveningMinutes, 0);
    
    // إذا كان الوقت الحالي بعد وقت الصباح، نضبط وقت الصباح ليوم غد
    if (now > morningTime) {
      morningTime.setDate(morningTime.getDate() + 1);
    }
    
    // إذا كان الوقت الحالي بعد وقت المساء، نضبط وقت المساء ليوم غد
    if (now > eveningTime) {
      eveningTime.setDate(eveningTime.getDate() + 1);
    }
    
    // تحديد الذكر القادم (الأقرب زمنياً)
    let nextAzkarType, nextAzkarTime;
    
    if (morningTime < eveningTime) {
      nextAzkarType = "أذكار الصباح";
      nextAzkarTime = morningTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    } else {
      nextAzkarType = "أذكار المساء";
      nextAzkarTime = eveningTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    }
    
    document.getElementById('next-azkar-type').textContent = nextAzkarType;
    document.getElementById('next-azkar-time').textContent = nextAzkarTime;
  });
}

// تحميل الأذكار
function loadAzkar() {
  try {
    // أذكار الصباح
    const morningContainer = document.getElementById('morning-azkar');
    if (morningContainer) {
      if (typeof morningAzkar !== 'undefined' && Array.isArray(morningAzkar)) {
        morningAzkar.forEach((zikr, index) => {
          morningContainer.appendChild(createZikrElement(zikr, index));
        });
      } else {
        morningContainer.innerHTML = '<p class="error-message">فشل في تحميل أذكار الصباح. يرجى إعادة تحميل الإضافة.</p>';
      }
    }

    // أذكار المساء
    const eveningContainer = document.getElementById('evening-azkar');
    if (eveningContainer) {
      if (typeof eveningAzkar !== 'undefined' && Array.isArray(eveningAzkar)) {
        eveningAzkar.forEach((zikr, index) => {
          eveningContainer.appendChild(createZikrElement(zikr, index));
        });
      } else {
        eveningContainer.innerHTML = '<p class="error-message">فشل في تحميل أذكار المساء. يرجى إعادة تحميل الإضافة.</p>';
      }
    }

    // أذكار بعد الصلاة
    const prayerContainer = document.getElementById('prayer-azkar');
    if (prayerContainer) {
      if (typeof afterPrayerAzkar !== 'undefined' && Array.isArray(afterPrayerAzkar)) {
        afterPrayerAzkar.forEach((zikr, index) => {
          prayerContainer.appendChild(createZikrElement(zikr, index));
        });
      } else {
        prayerContainer.innerHTML = '<p class="error-message">فشل في تحميل أذكار بعد الصلاة. يرجى إعادة تحميل الإضافة.</p>';
      }
    }
  } catch (error) {
    console.error('خطأ في تحميل الأذكار:', error);
    alert('حدث خطأ في تحميل الأذكار. يرجى إعادة تحميل الإضافة.');
  }
}

// إنشاء عنصر ذكر
function createZikrElement(zikr, index) {
  const zikrElement = document.createElement('div');
  zikrElement.className = 'zikr-item';

  const zikrText = document.createElement('div');
  zikrText.className = 'zikr-text';
  zikrText.textContent = zikr.text;

  const zikrTranslation = document.createElement('div');
  zikrTranslation.className = 'zikr-translation';
  zikrTranslation.textContent = zikr.translation || '';

  const zikrCountContainer = document.createElement('div');
  zikrCountContainer.className = 'zikr-count-container';

  const zikrCount = document.createElement('div');
  zikrCount.className = 'zikr-count';
  zikrCount.textContent = `عدد المرات: ${zikr.count}`;

  // إضافة عداد تفاعلي إذا كان العدد أكثر من 1
  if (zikr.count > 1) {
    const counterContainer = document.createElement('div');
    counterContainer.className = 'counter-container';

    const counterDisplay = document.createElement('span');
    counterDisplay.className = 'counter-display';
    counterDisplay.textContent = `0 / ${zikr.count}`;

    const incrementButton = document.createElement('button');
    incrementButton.className = 'counter-btn';
    incrementButton.textContent = '+';
    incrementButton.onclick = () => incrementCounter(counterDisplay, zikr.count);

    const resetButton = document.createElement('button');
    resetButton.className = 'counter-btn reset-btn';
    resetButton.textContent = 'إعادة تعيين';
    resetButton.onclick = () => resetCounter(counterDisplay, zikr.count);

    counterContainer.appendChild(counterDisplay);
    counterContainer.appendChild(incrementButton);
    counterContainer.appendChild(resetButton);
    zikrCountContainer.appendChild(counterContainer);
  }

  zikrElement.appendChild(zikrText);
  if (zikr.translation) {
    zikrElement.appendChild(zikrTranslation);
  }
  zikrElement.appendChild(zikrCount);
  zikrElement.appendChild(zikrCountContainer);

  return zikrElement;
}

// زيادة العداد
function incrementCounter(display, maxCount) {
  const currentText = display.textContent;
  const currentCount = parseInt(currentText.split(' / ')[0]);

  if (currentCount < maxCount) {
    const newCount = currentCount + 1;
    display.textContent = `${newCount} / ${maxCount}`;

    if (newCount === maxCount) {
      display.style.color = '#4CAF50';
      display.style.fontWeight = 'bold';
    }
  }
}

// إعادة تعيين العداد
function resetCounter(display, maxCount) {
  display.textContent = `0 / ${maxCount}`;
  display.style.color = '';
  display.style.fontWeight = '';
}

// إعداد التبويبات
function setupTabs() {
  const tabs = document.querySelectorAll('.tab');
  const tabContents = document.querySelectorAll('.tab-content');
  
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // إزالة الفئة النشطة من جميع التبويبات
      tabs.forEach(t => t.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));
      
      // إضافة الفئة النشطة للتبويب المحدد
      tab.classList.add('active');
      const tabId = tab.getAttribute('data-tab');
      document.getElementById(tabId).classList.add('active');
    });
  });
}
