document.addEventListener('DOMContentLoaded', function() {
  // تحميل الإعدادات
  loadSettings();
  
  // عرض الوقت الحالي
  updateCurrentTime();
  setInterval(updateCurrentTime, 1000);
  
  // عرض معلومات الذكر القادم
  updateNextAzkar();
  
  // تحميل الأذكار
  loadAzkar();
  
  // إعداد التبويبات
  setupTabs();
  
  // إعداد زر حفظ الإعدادات
  document.getElementById('save-settings').addEventListener('click', saveSettings);
});

// تحديث الوقت الحالي
function updateCurrentTime() {
  const now = new Date();
  const timeString = now.toLocaleTimeString('ar-SA');
  document.getElementById('current-time').textContent = timeString;
}

// تحميل الإعدادات
function loadSettings() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || {
      morning: "05:00",
      evening: "17:00",
      afterPrayer: true
    };
    
    document.getElementById('morning-time').value = times.morning;
    document.getElementById('evening-time').value = times.evening;
    document.getElementById('after-prayer').checked = times.afterPrayer;
  });
}

// حفظ الإعدادات
function saveSettings() {
  const morningTime = document.getElementById('morning-time').value;
  const eveningTime = document.getElementById('evening-time').value;
  const afterPrayer = document.getElementById('after-prayer').checked;
  
  const settings = {
    morning: morningTime,
    evening: eveningTime,
    afterPrayer: afterPrayer
  };
  
  chrome.storage.local.set({ azkarTimes: settings }, () => {
    alert('تم حفظ الإعدادات بنجاح');
    updateNextAzkar();
  });
}

// تحديث معلومات الذكر القادم
function updateNextAzkar() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || {
      morning: "05:00",
      evening: "17:00"
    };
    
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    const [morningHours, morningMinutes] = times.morning.split(':').map(Number);
    const [eveningHours, eveningMinutes] = times.evening.split(':').map(Number);
    
    let morningTime = new Date();
    morningTime.setHours(morningHours, morningMinutes, 0);
    
    let eveningTime = new Date();
    eveningTime.setHours(eveningHours, eveningMinutes, 0);
    
    // إذا كان الوقت الحالي بعد وقت الصباح، نضبط وقت الصباح ليوم غد
    if (now > morningTime) {
      morningTime.setDate(morningTime.getDate() + 1);
    }
    
    // إذا كان الوقت الحالي بعد وقت المساء، نضبط وقت المساء ليوم غد
    if (now > eveningTime) {
      eveningTime.setDate(eveningTime.getDate() + 1);
    }
    
    // تحديد الذكر القادم (الأقرب زمنياً)
    let nextAzkarType, nextAzkarTime;
    
    if (morningTime < eveningTime) {
      nextAzkarType = "أذكار الصباح";
      nextAzkarTime = morningTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    } else {
      nextAzkarType = "أذكار المساء";
      nextAzkarTime = eveningTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    }
    
    document.getElementById('next-azkar-type').textContent = nextAzkarType;
    document.getElementById('next-azkar-time').textContent = nextAzkarTime;
  });
}

// تحميل الأذكار
function loadAzkar() {
  // أذكار الصباح
  const morningContainer = document.getElementById('morning-azkar');
  morningAzkar.forEach(zikr => {
    morningContainer.appendChild(createZikrElement(zikr));
  });
  
  // أذكار المساء
  const eveningContainer = document.getElementById('evening-azkar');
  eveningAzkar.forEach(zikr => {
    eveningContainer.appendChild(createZikrElement(zikr));
  });
  
  // أذكار بعد الصلاة
  const prayerContainer = document.getElementById('prayer-azkar');
  afterPrayerAzkar.forEach(zikr => {
    prayerContainer.appendChild(createZikrElement(zikr));
  });
}

// إنشاء عنصر ذكر
function createZikrElement(zikr) {
  const zikrElement = document.createElement('div');
  zikrElement.className = 'zikr-item';
  
  const zikrText = document.createElement('div');
  zikrText.className = 'zikr-text';
  zikrText.textContent = zikr.text;
  
  const zikrTranslation = document.createElement('div');
  zikrTranslation.className = 'zikr-translation';
  zikrTranslation.textContent = zikr.translation || '';
  
  const zikrCount = document.createElement('div');
  zikrCount.className = 'zikr-count';
  zikrCount.textContent = `عدد المرات: ${zikr.count}`;
  
  zikrElement.appendChild(zikrText);
  if (zikr.translation) {
    zikrElement.appendChild(zikrTranslation);
  }
  zikrElement.appendChild(zikrCount);
  
  return zikrElement;
}

// إعداد التبويبات
function setupTabs() {
  const tabs = document.querySelectorAll('.tab');
  const tabContents = document.querySelectorAll('.tab-content');
  
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // إزالة الفئة النشطة من جميع التبويبات
      tabs.forEach(t => t.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));
      
      // إضافة الفئة النشطة للتبويب المحدد
      tab.classList.add('active');
      const tabId = tab.getAttribute('data-tab');
      document.getElementById(tabId).classList.add('active');
    });
  });
}
