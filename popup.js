// متغيرات الصوت العامة
let currentAudio = null;
let audioEnabled = true;

document.addEventListener('DOMContentLoaded', function() {
  // تحميل الإعدادات
  loadSettings();

  // عرض الوقت الحالي
  updateCurrentTime();
  setInterval(updateCurrentTime, 1000);

  // عرض معلومات الذكر القادم
  updateNextAzkar();

  // تحميل الأذكار
  loadAzkar();

  // إعداد التبويبات
  setupTabs();

  // إعداد زر حفظ الإعدادات
  document.getElementById('save-settings').addEventListener('click', saveSettings);

  // تحميل إعدادات الصوت
  loadAudioSettings();

  // تهيئة المميزات المحسنة
  if (typeof initEnhancedFeatures === 'function') {
    initEnhancedFeatures();
  }

  // تهيئة أوقات الصلاة
  initPrayerTimes();

  // تهيئة اختيار الثيمات
  initThemeSelector();

  // تهيئة التقويم الهجري
  initHijriCalendar();

  // تهيئة أذكار رمضان
  initRamadanAzkar();

  // تهيئة نظام الإنجازات
  initAchievements();

  // تهيئة دعم اللغات
  initLanguageSupport();

  // تهيئة النسخ الاحتياطي
  initBackupSystem();
});

// تحديث الوقت الحالي
function updateCurrentTime() {
  const now = new Date();
  const timeString = now.toLocaleTimeString('ar-SA');
  document.getElementById('current-time').textContent = timeString;
}

// التحقق من صحة الوقت
function validateTime(timeString) {
  if (!timeString || typeof timeString !== 'string') {
    return false;
  }

  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  if (!timeRegex.test(timeString)) {
    return false;
  }

  const [hours, minutes] = timeString.split(':').map(Number);
  return hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59;
}

// تحميل الإعدادات
function loadSettings() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || {
      morning: "05:00",
      evening: "17:00",
      afterPrayer: true
    };
    
    document.getElementById('morning-time').value = times.morning;
    document.getElementById('evening-time').value = times.evening;
    document.getElementById('after-prayer').checked = times.afterPrayer;
  });
}

// حفظ الإعدادات
function saveSettings() {
  try {
    const morningTimeElement = document.getElementById('morning-time');
    const eveningTimeElement = document.getElementById('evening-time');
    const afterPrayerElement = document.getElementById('after-prayer');

    if (!morningTimeElement || !eveningTimeElement || !afterPrayerElement) {
      alert('خطأ: لا يمكن العثور على عناصر الإعدادات');
      return;
    }

    const morningTime = morningTimeElement.value;
    const eveningTime = eveningTimeElement.value;
    const afterPrayer = afterPrayerElement.checked;

    // التحقق من صحة الأوقات
    if (!morningTime || !eveningTime) {
      alert('يرجى تحديد أوقات صحيحة للأذكار');
      return;
    }

    // التحقق من صحة تنسيق الوقت
    if (!validateTime(morningTime) || !validateTime(eveningTime)) {
      alert('يرجى إدخال أوقات صحيحة (00:00 - 23:59)');
      return;
    }

    // التحقق من أن وقت المساء مختلف عن وقت الصباح
    if (morningTime === eveningTime) {
      alert('يجب أن يكون وقت أذكار المساء مختلفاً عن وقت أذكار الصباح');
      return;
    }

    // إعدادات الصوت
    const audioEnabledElement = document.getElementById('audio-enabled');
    const audioVolumeElement = document.getElementById('audio-volume');

    const settings = {
      morning: morningTime,
      evening: eveningTime,
      afterPrayer: afterPrayer
    };

    const audioSettings = {
      enabled: audioEnabledElement ? audioEnabledElement.checked : true,
      volume: audioVolumeElement ? parseFloat(audioVolumeElement.value) : 0.7
    };

    // تحديث المتغير العام
    audioEnabled = audioSettings.enabled;

    // حفظ الإعدادات
    chrome.storage.local.set({
      azkarTimes: settings,
      audioSettings: audioSettings
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('خطأ في حفظ الإعدادات:', chrome.runtime.lastError);
        alert('حدث خطأ في حفظ الإعدادات. يرجى المحاولة مرة أخرى.');
      } else {
        alert('تم حفظ الإعدادات بنجاح');
        updateNextAzkar();
      }
    });
  } catch (error) {
    console.error('خطأ في حفظ الإعدادات:', error);
    alert('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
  }
}

// تحديث معلومات الذكر القادم
function updateNextAzkar() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || {
      morning: "05:00",
      evening: "17:00"
    };
    
    const now = new Date();
    
    const [morningHours, morningMinutes] = times.morning.split(':').map(Number);
    const [eveningHours, eveningMinutes] = times.evening.split(':').map(Number);
    
    let morningTime = new Date();
    morningTime.setHours(morningHours, morningMinutes, 0);
    
    let eveningTime = new Date();
    eveningTime.setHours(eveningHours, eveningMinutes, 0);
    
    // إذا كان الوقت الحالي بعد وقت الصباح، نضبط وقت الصباح ليوم غد
    if (now > morningTime) {
      morningTime.setDate(morningTime.getDate() + 1);
    }
    
    // إذا كان الوقت الحالي بعد وقت المساء، نضبط وقت المساء ليوم غد
    if (now > eveningTime) {
      eveningTime.setDate(eveningTime.getDate() + 1);
    }
    
    // تحديد الذكر القادم (الأقرب زمنياً)
    let nextAzkarType, nextAzkarTime;
    
    if (morningTime < eveningTime) {
      nextAzkarType = "أذكار الصباح";
      nextAzkarTime = morningTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    } else {
      nextAzkarType = "أذكار المساء";
      nextAzkarTime = eveningTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    }
    
    document.getElementById('next-azkar-type').textContent = nextAzkarType;
    document.getElementById('next-azkar-time').textContent = nextAzkarTime;
  });
}

// تحميل الأذكار
function loadAzkar() {
  try {
    // أذكار الصباح
    const morningContainer = document.getElementById('morning-azkar');
    if (morningContainer) {
      if (typeof morningAzkar !== 'undefined' && Array.isArray(morningAzkar)) {
        morningAzkar.forEach((zikr) => {
          morningContainer.appendChild(createZikrElement(zikr, 'morning'));
        });
      } else {
        morningContainer.innerHTML = '<p class="error-message">فشل في تحميل أذكار الصباح. يرجى إعادة تحميل الإضافة.</p>';
      }
    }

    // أذكار المساء
    const eveningContainer = document.getElementById('evening-azkar');
    if (eveningContainer) {
      if (typeof eveningAzkar !== 'undefined' && Array.isArray(eveningAzkar)) {
        eveningAzkar.forEach((zikr) => {
          eveningContainer.appendChild(createZikrElement(zikr, 'evening'));
        });
      } else {
        eveningContainer.innerHTML = '<p class="error-message">فشل في تحميل أذكار المساء. يرجى إعادة تحميل الإضافة.</p>';
      }
    }

    // أذكار بعد الصلاة
    const prayerContainer = document.getElementById('prayer-azkar');
    if (prayerContainer) {
      if (typeof afterPrayerAzkar !== 'undefined' && Array.isArray(afterPrayerAzkar)) {
        afterPrayerAzkar.forEach((zikr) => {
          prayerContainer.appendChild(createZikrElement(zikr, 'prayer'));
        });
      } else {
        prayerContainer.innerHTML = '<p class="error-message">فشل في تحميل أذكار بعد الصلاة. يرجى إعادة تحميل الإضافة.</p>';
      }
    }
  } catch (error) {
    console.error('خطأ في تحميل الأذكار:', error);
    alert('حدث خطأ في تحميل الأذكار. يرجى إعادة تحميل الإضافة.');
  }
}

// إنشاء عنصر ذكر
function createZikrElement(zikr, category = 'morning') {
  const zikrElement = document.createElement('div');
  zikrElement.className = 'zikr-item';

  // إضافة أزرار التحكم في الصوت
  const audioControls = document.createElement('div');
  audioControls.className = 'audio-controls';

  const audioPath = getAudioPath(zikr.text, category);
  if (audioPath) {
    const playButton = document.createElement('button');
    playButton.className = 'audio-btn play-btn';
    playButton.innerHTML = '🔊';
    playButton.title = 'تشغيل التلاوة';
    playButton.onclick = () => playAudio(audioPath);

    const stopButton = document.createElement('button');
    stopButton.className = 'audio-btn stop-btn';
    stopButton.innerHTML = '⏹️';
    stopButton.title = 'إيقاف التلاوة';
    stopButton.onclick = () => stopAudio();

    audioControls.appendChild(playButton);
    audioControls.appendChild(stopButton);
  }

  const zikrText = document.createElement('div');
  zikrText.className = 'zikr-text';
  zikrText.textContent = zikr.text;

  const zikrTranslation = document.createElement('div');
  zikrTranslation.className = 'zikr-translation';
  zikrTranslation.textContent = zikr.translation || '';

  const zikrCountContainer = document.createElement('div');
  zikrCountContainer.className = 'zikr-count-container';

  const zikrCount = document.createElement('div');
  zikrCount.className = 'zikr-count';
  zikrCount.textContent = `عدد المرات: ${zikr.count}`;

  // إضافة عداد تفاعلي إذا كان العدد أكثر من 1
  if (zikr.count > 1) {
    const counterContainer = document.createElement('div');
    counterContainer.className = 'counter-container';

    const counterDisplay = document.createElement('span');
    counterDisplay.className = 'counter-display';
    counterDisplay.textContent = `0 / ${zikr.count}`;

    const incrementButton = document.createElement('button');
    incrementButton.className = 'counter-btn';
    incrementButton.textContent = '+';
    incrementButton.onclick = () => incrementCounter(counterDisplay, zikr.count);

    const resetButton = document.createElement('button');
    resetButton.className = 'counter-btn reset-btn';
    resetButton.textContent = 'إعادة تعيين';
    resetButton.onclick = () => resetCounter(counterDisplay, zikr.count);

    counterContainer.appendChild(counterDisplay);
    counterContainer.appendChild(incrementButton);
    counterContainer.appendChild(resetButton);
    zikrCountContainer.appendChild(counterContainer);
  }

  // إضافة زر المشاركة
  const shareButton = document.createElement('button');
  shareButton.className = 'share-button';
  shareButton.innerHTML = '📤';
  shareButton.title = 'مشاركة الذكر';
  shareButton.onclick = () => shareZikr(zikr.text, zikr.translation || '');

  // ترتيب العناصر
  zikrElement.appendChild(audioControls);
  zikrElement.appendChild(zikrText);
  if (zikr.translation) {
    zikrElement.appendChild(zikrTranslation);
  }
  zikrElement.appendChild(zikrCount);
  zikrElement.appendChild(zikrCountContainer);
  zikrElement.appendChild(shareButton);

  return zikrElement;
}

// زيادة العداد
function incrementCounter(display, maxCount) {
  const currentText = display.textContent;
  const currentCount = parseInt(currentText.split(' / ')[0]);

  if (currentCount < maxCount) {
    const newCount = currentCount + 1;
    display.textContent = `${newCount} / ${maxCount}`;

    if (newCount === maxCount) {
      display.style.color = '#4CAF50';
      display.style.fontWeight = 'bold';
    }
  }
}

// إعادة تعيين العداد
function resetCounter(display, maxCount) {
  display.textContent = `0 / ${maxCount}`;
  display.style.color = '';
  display.style.fontWeight = '';
}

// وظائف الصوت
function loadAudioSettings() {
  chrome.storage.local.get(['audioSettings'], (result) => {
    const settings = result.audioSettings || { enabled: true, volume: 0.7 };
    audioEnabled = settings.enabled;

    // إضافة عنصر تحكم الصوت في الإعدادات إذا لم يكن موجود
    addAudioControlsToSettings();
  });
}

function addAudioControlsToSettings() {
  const settingsForm = document.querySelector('.settings-form');
  if (settingsForm && !document.getElementById('audio-enabled')) {
    const audioGroup = document.createElement('div');
    audioGroup.className = 'form-group';
    audioGroup.innerHTML = `
      <label for="audio-enabled">تفعيل التلاوة الصوتية:</label>
      <input type="checkbox" id="audio-enabled" name="audio-enabled" ${audioEnabled ? 'checked' : ''}>
    `;

    const volumeGroup = document.createElement('div');
    volumeGroup.className = 'form-group';
    volumeGroup.innerHTML = `
      <label for="audio-volume">مستوى الصوت:</label>
      <input type="range" id="audio-volume" name="audio-volume" min="0" max="1" step="0.1" value="0.7">
    `;

    settingsForm.insertBefore(audioGroup, settingsForm.lastElementChild);
    settingsForm.insertBefore(volumeGroup, settingsForm.lastElementChild);
  }
}

function playAudio(audioPath) {
  if (!audioEnabled) return;

  try {
    // إيقاف الصوت الحالي إذا كان يعمل
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
    }

    // تشغيل الصوت الجديد
    currentAudio = new Audio(chrome.runtime.getURL(audioPath));
    currentAudio.volume = document.getElementById('audio-volume')?.value || 0.7;

    currentAudio.play().catch(error => {
      console.warn('لا يمكن تشغيل الملف الصوتي:', error);
      // عرض رسالة للمستخدم
      showAudioError('لا يمكن تشغيل الملف الصوتي. تأكد من وجود الملفات الصوتية.');
    });

  } catch (error) {
    console.error('خطأ في تشغيل الصوت:', error);
  }
}

function stopAudio() {
  if (currentAudio) {
    currentAudio.pause();
    currentAudio.currentTime = 0;
    currentAudio = null;
  }
}

// عرض خطأ الصوت
function showAudioError(message) {
  // إنشاء عنصر الخطأ
  const errorDiv = document.createElement('div');
  errorDiv.className = 'audio-error-message';
  errorDiv.textContent = message;
  errorDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #f44336;
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    z-index: 1000;
    font-size: 14px;
    max-width: 300px;
  `;

  document.body.appendChild(errorDiv);

  // إزالة الرسالة بعد 3 ثوان
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 3000);
}

function getAudioPath(zikrText, category) {
  // تحديد مسار الملف الصوتي بناءً على نص الذكر والفئة
  const audioMap = {
    'آية الكرسي': 'audio/' + category + '/ayat-kursi.mp3',
    'سورة الإخلاص': 'audio/' + category + '/ikhlas.mp3',
    'سورة الفلق': 'audio/' + category + '/falaq.mp3',
    'سورة الناس': 'audio/' + category + '/nas.mp3',
    'سبحان الله': 'audio/prayer/tasbih.mp3',
    'الحمد لله': 'audio/prayer/tahmid.mp3',
    'الله أكبر': 'audio/prayer/takbir.mp3'
  };

  // البحث عن تطابق في النص
  for (const [key, path] of Object.entries(audioMap)) {
    if (zikrText.includes(key)) {
      return path;
    }
  }

  return null; // لا يوجد ملف صوتي متاح
}

// إعداد التبويبات
function setupTabs() {
  const tabs = document.querySelectorAll('.tab');
  const tabContents = document.querySelectorAll('.tab-content');

  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // إزالة الفئة النشطة من جميع التبويبات
      tabs.forEach(t => t.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));

      // إضافة الفئة النشطة للتبويب المحدد
      tab.classList.add('active');
      const tabId = tab.getAttribute('data-tab');
      document.getElementById(tabId).classList.add('active');
    });
  });
}

// تهيئة أوقات الصلاة
async function initPrayerTimes() {
  try {
    if (!window.prayerTimesManager) {
      console.warn('مدير أوقات الصلاة غير متاح');
      return;
    }

    // محاولة تحميل أوقات الصلاة المحفوظة أولاً
    let prayerTimes = await prayerTimesManager.loadSavedPrayerTimes();

    if (prayerTimes) {
      displayPrayerTimes(prayerTimes);
    } else {
      showPrayerTimesLoading();
    }

    // تحديث أوقات الصلاة
    try {
      prayerTimes = await prayerTimesManager.updatePrayerTimes();
      displayPrayerTimes(prayerTimes);

      // إعداد تذكيرات الصلاة
      await prayerTimesManager.setupPrayerReminders();
    } catch (error) {
      showPrayerTimesError(error.message);
    }
  } catch (error) {
    console.error('خطأ في تهيئة أوقات الصلاة:', error);
  }
}

// عرض أوقات الصلاة
function displayPrayerTimes(prayerTimes) {
  const grid = document.getElementById('prayer-times-grid');
  const nextPrayerInfo = document.getElementById('next-prayer-info');
  const hijriDateDisplay = document.getElementById('hijri-date-display');

  if (!grid) return;

  // عرض أوقات الصلاة
  const prayers = [
    { key: 'fajr', name: 'الفجر', time: prayerTimes.timings.fajr },
    { key: 'dhuhr', name: 'الظهر', time: prayerTimes.timings.dhuhr },
    { key: 'asr', name: 'العصر', time: prayerTimes.timings.asr },
    { key: 'maghrib', name: 'المغرب', time: prayerTimes.timings.maghrib },
    { key: 'isha', name: 'العشاء', time: prayerTimes.timings.isha }
  ];

  // الحصول على الصلاة القادمة
  const nextPrayer = prayerTimesManager.getNextPrayer();

  grid.innerHTML = prayers.map(prayer => {
    const isNext = nextPrayer && nextPrayer.name === prayer.key;
    return `
      <div class="prayer-time-item ${isNext ? 'next-prayer' : ''}">
        <div class="prayer-name">${prayer.name}</div>
        <div class="prayer-time">${prayer.time.time12}</div>
      </div>
    `;
  }).join('');

  // عرض معلومات الصلاة القادمة
  if (nextPrayer && nextPrayerInfo) {
    const timeRemaining = nextPrayer.timeRemaining;
    if (timeRemaining) {
      nextPrayerInfo.textContent = `${nextPrayer.arabicName} خلال ${timeRemaining.formatted}`;
    } else {
      nextPrayerInfo.textContent = `${nextPrayer.arabicName} في ${nextPrayer.time.time12}`;
    }
  }

  // عرض التاريخ الهجري
  const hijriDate = prayerTimesManager.getHijriDate();
  if (hijriDate && hijriDateDisplay) {
    hijriDateDisplay.textContent = hijriDate.formatted;
  }
}

// عرض حالة التحميل
function showPrayerTimesLoading() {
  const grid = document.getElementById('prayer-times-grid');
  if (grid) {
    grid.innerHTML = '<div class="loading">جاري تحميل أوقات الصلاة...</div>';
  }
}

// عرض خطأ أوقات الصلاة
function showPrayerTimesError(errorMessage) {
  const grid = document.getElementById('prayer-times-grid');
  if (grid) {
    grid.innerHTML = `
      <div class="prayer-times-error">
        <p>⚠️ خطأ في تحميل أوقات الصلاة</p>
        <p>${errorMessage}</p>
        <div class="location-permission">
          <p>يرجى السماح بالوصول للموقع الجغرافي</p>
          <button onclick="requestLocationPermission()">السماح بالوصول للموقع</button>
        </div>
      </div>
    `;
  }
}

// طلب إذن الموقع
async function requestLocationPermission() {
  try {
    await prayerTimesManager.getCurrentLocation();
    initPrayerTimes(); // إعادة المحاولة
  } catch (error) {
    alert('لا يمكن الوصول للموقع الجغرافي. يرجى تفعيل خدمات الموقع في المتصفح.');
  }
}

// تهيئة اختيار الثيمات
function initThemeSelector() {
  const themeSelector = document.getElementById('theme-selector');
  if (!themeSelector || !window.themeManager) return;

  const themes = themeManager.getAllThemes();
  const currentTheme = themeManager.currentTheme;

  themeSelector.innerHTML = Object.entries(themes).map(([id, theme]) => `
    <div class="theme-option ${id === currentTheme ? 'active' : ''}" data-theme-id="${id}">
      <div class="theme-icon">${theme.icon}</div>
      <div class="theme-name">${theme.name}</div>
      <div class="theme-description">${theme.description}</div>
    </div>
  `).join('');

  // إضافة مستمعي الأحداث
  themeSelector.addEventListener('click', (e) => {
    const themeOption = e.target.closest('.theme-option');
    if (themeOption) {
      const themeId = themeOption.dataset.themeId;

      // إزالة التحديد السابق
      themeSelector.querySelectorAll('.theme-option').forEach(option => {
        option.classList.remove('active');
      });

      // تحديد الثيم الجديد
      themeOption.classList.add('active');

      // تطبيق الثيم
      themeManager.applyTheme(themeId);
    }
  });
}

// تهيئة التقويم الهجري
async function initHijriCalendar() {
  try {
    if (!window.hijriCalendarManager) {
      console.warn('مدير التقويم الهجري غير متاح');
      return;
    }

    // عرض التاريخ الهجري
    const hijriDate = hijriCalendarManager.getCurrentHijriDate();
    if (hijriDate) {
      const hijriDisplay = document.getElementById('hijri-date-display');
      if (hijriDisplay) {
        hijriDisplay.textContent = hijriDate.formatted;
      }

      // عرض معلومات الشهر
      displayHijriMonthInfo();
    }

    // عرض المناسبات الإسلامية
    displayIslamicEvents();

    // إعداد تذكيرات المناسبات
    await hijriCalendarManager.setupEventReminders();

  } catch (error) {
    console.error('خطأ في تهيئة التقويم الهجري:', error);
  }
}

// عرض معلومات الشهر الهجري
function displayHijriMonthInfo() {
  const monthInfo = hijriCalendarManager.getCurrentMonthInfo();
  if (!monthInfo) return;

  const hijriMonthInfoDiv = document.getElementById('hijri-month-info');
  const hijriProgress = document.getElementById('hijri-progress');
  const hijriProgressText = document.getElementById('hijri-progress-text');

  if (hijriMonthInfoDiv && hijriProgress && hijriProgressText) {
    hijriMonthInfoDiv.style.display = 'block';
    hijriProgress.style.width = `${monthInfo.progress}%`;
    hijriProgressText.textContent = `${monthInfo.day}/${monthInfo.monthLength} أيام`;
  }
}

// عرض المناسبات الإسلامية
function displayIslamicEvents() {
  const eventsList = document.getElementById('events-list');
  if (!eventsList) return;

  const upcomingEvents = hijriCalendarManager.getUpcomingEvents(7);

  if (upcomingEvents.length === 0) {
    eventsList.innerHTML = '<div class="no-events">لا توجد مناسبات قريبة</div>';
    return;
  }

  eventsList.innerHTML = upcomingEvents.slice(0, 5).map(event => {
    const eventIcon = getEventIcon(event.type);
    const daysText = event.daysFromNow === 0 ? 'اليوم' :
                     event.daysFromNow === 1 ? 'غداً' :
                     `خلال ${event.daysFromNow} أيام`;

    return `
      <div class="event-item event-importance-${event.importance}">
        <div class="event-icon">${eventIcon}</div>
        <div class="event-details">
          <div class="event-name">${event.name}</div>
          <div class="event-date">${daysText}</div>
        </div>
      </div>
    `;
  }).join('');
}

// الحصول على أيقونة المناسبة
function getEventIcon(eventType) {
  const icons = {
    'celebration': '🎉',
    'religious': '🕌',
    'ramadan': '🌙',
    'eid': '🎊',
    'hajj': '🕋',
    'sunnah': '⭐'
  };
  return icons[eventType] || '📅';
}

// تهيئة أذكار رمضان
async function initRamadanAzkar() {
  try {
    if (!window.ramadanAzkarManager) {
      console.warn('مدير أذكار رمضان غير متاح');
      return;
    }

    // التحقق من شهر رمضان
    const isRamadan = await ramadanAzkarManager.checkAndActivateRamadan();

    // إظهار/إخفاء تبويب رمضان
    const ramadanTab = document.querySelector('.ramadan-tab');
    if (ramadanTab) {
      ramadanTab.style.display = isRamadan ? 'block' : 'none';
    }

    if (isRamadan) {
      // عرض معلومات رمضان
      displayRamadanInfo();

      // عرض أذكار رمضان
      displayRamadanAzkar();

      // عرض نصائح رمضان
      displayRamadanTips();

      // عرض إحصائيات رمضان
      displayRamadanStats();
    }

  } catch (error) {
    console.error('خطأ في تهيئة أذكار رمضان:', error);
  }
}

// عرض معلومات رمضان
function displayRamadanInfo() {
  const ramadanInfo = ramadanAzkarManager.getRamadanInfo();
  if (!ramadanInfo) return;

  const ramadanDay = document.getElementById('ramadan-day');
  const daysRemaining = document.getElementById('days-remaining');
  const specialNight = document.getElementById('special-night');

  if (ramadanDay) ramadanDay.textContent = ramadanInfo.currentDay;
  if (daysRemaining) daysRemaining.textContent = ramadanInfo.daysRemaining;

  if (specialNight && ramadanInfo.isPossibleLaylatQadr) {
    specialNight.textContent = '✨ ليلة وترية محتملة لليلة القدر';
    specialNight.style.display = 'block';
  }

  // إظهار أقسام خاصة
  const laylatQadrSection = document.getElementById('laylat-qadr-section');
  const lastTenSection = document.getElementById('last-ten-section');

  if (laylatQadrSection && ramadanInfo.isPossibleLaylatQadr) {
    laylatQadrSection.style.display = 'block';
  }

  if (lastTenSection && ramadanInfo.isLastTenNights) {
    lastTenSection.style.display = 'block';
  }
}

// عرض أذكار رمضان
function displayRamadanAzkar() {
  // أذكار الإفطار
  const iftarAzkar = ramadanAzkarManager.getAzkarByType('iftar');
  displayAzkarInContainer('iftar-azkar', iftarAzkar, 'ramadan-iftar');

  // أذكار السحور
  const suhoorAzkar = ramadanAzkarManager.getAzkarByType('suhoor');
  displayAzkarInContainer('suhoor-azkar', suhoorAzkar, 'ramadan-suhoor');

  // أذكار ليلة القدر
  const laylatQadrAzkar = ramadanAzkarManager.getAzkarByType('laylatAlQadr');
  displayAzkarInContainer('laylat-qadr-azkar', laylatQadrAzkar, 'ramadan-laylat-qadr');

  // أذكار العشر الأواخر
  const lastTenAzkar = ramadanAzkarManager.getAzkarByType('lastTenNights');
  displayAzkarInContainer('last-ten-azkar', lastTenAzkar, 'ramadan-last-ten');
}

// عرض نصائح رمضان
function displayRamadanTips() {
  const tipsList = document.getElementById('tips-list');
  if (!tipsList) return;

  const tips = ramadanAzkarManager.getTodayRamadanTips();

  tipsList.innerHTML = tips.map(tip =>
    `<div class="tip-item">${tip}</div>`
  ).join('');
}

// عرض إحصائيات رمضان
async function displayRamadanStats() {
  const stats = await ramadanAzkarManager.getRamadanStats();
  if (!stats) return;

  const iftarCount = document.getElementById('iftar-count');
  const suhoorCount = document.getElementById('suhoor-count');
  const qadrCount = document.getElementById('qadr-count');

  if (iftarCount) iftarCount.textContent = stats.iftarAzkarRead;
  if (suhoorCount) suhoorCount.textContent = stats.suhoorAzkarRead;
  if (qadrCount) qadrCount.textContent = stats.laylatQadrAzkarRead;
}

// تهيئة نظام الإنجازات
async function initAchievements() {
  try {
    if (!window.achievementsManager) {
      console.warn('مدير الإنجازات غير متاح');
      return;
    }

    // عرض إحصائيات المستخدم
    displayUserStats();

    // عرض التحديات النشطة
    displayActiveChallenges();

    // عرض معرض الإنجازات
    displayAchievementsGallery();

    // عرض الإنجازات الحديثة
    displayRecentAchievements();

    // إعداد مرشحات الإنجازات
    setupAchievementFilters();

  } catch (error) {
    console.error('خطأ في تهيئة نظام الإنجازات:', error);
  }
}

// عرض إحصائيات المستخدم
function displayUserStats() {
  const stats = achievementsManager.getUserStats();

  // تحديث مستوى المستخدم
  const userLevel = document.getElementById('user-level');
  const levelDisplay = document.getElementById('level-display');
  const levelProgress = document.getElementById('level-progress');
  const levelProgressText = document.getElementById('level-progress-text');

  if (userLevel) userLevel.textContent = stats.level;
  if (levelDisplay) levelDisplay.textContent = stats.level;
  if (levelProgress) levelProgress.style.width = `${stats.levelProgress}%`;
  if (levelProgressText) {
    const nextLevelPoints = (stats.level * 100);
    levelProgressText.textContent = `${stats.totalPoints}/${nextLevelPoints} نقطة`;
  }

  // تحديث الإحصائيات العامة
  const totalReads = document.getElementById('total-reads');
  const currentStreak = document.getElementById('current-streak');
  const totalPoints = document.getElementById('total-points');
  const achievementsCount = document.getElementById('achievements-count');

  if (totalReads) totalReads.textContent = stats.totalReads;
  if (currentStreak) currentStreak.textContent = stats.currentStreak;
  if (totalPoints) totalPoints.textContent = stats.totalPoints;
  if (achievementsCount) achievementsCount.textContent = stats.achievementsCount;
}

// عرض التحديات النشطة
function displayActiveChallenges() {
  const challengesList = document.getElementById('challenges-list');
  if (!challengesList) return;

  const challenges = achievementsManager.getActiveChallenges();

  if (challenges.length === 0) {
    challengesList.innerHTML = '<div class="no-challenges">لا توجد تحديات نشطة</div>';
    return;
  }

  challengesList.innerHTML = challenges.map(challenge => `
    <div class="challenge-item">
      <div class="challenge-header">
        <div class="challenge-name">${challenge.name}</div>
        <div class="challenge-points">+${challenge.points} نقطة</div>
      </div>
      <div class="challenge-description">${challenge.description}</div>
      <div class="challenge-progress">
        <div class="progress-bar">
          <div class="progress-fill" style="width: ${challenge.progressPercent}%"></div>
        </div>
        <div class="challenge-progress-text">${challenge.progress}/${challenge.target}</div>
      </div>
    </div>
  `).join('');
}

// عرض معرض الإنجازات
function displayAchievementsGallery() {
  const achievementsGrid = document.getElementById('achievements-grid');
  if (!achievementsGrid) return;

  const leaderboard = achievementsManager.getLeaderboard();

  achievementsGrid.innerHTML = leaderboard.map(achievement => `
    <div class="achievement-card ${achievement.unlocked ? 'unlocked' : 'locked'}">
      <div class="achievement-icon">${achievement.icon}</div>
      <div class="achievement-name">${achievement.name}</div>
      <div class="achievement-description">${achievement.description}</div>
      <div class="achievement-points">+${achievement.points} نقطة</div>
    </div>
  `).join('');
}

// عرض الإنجازات الحديثة
async function displayRecentAchievements() {
  const recentList = document.getElementById('recent-achievements');
  if (!recentList) return;

  const recentAchievements = await achievementsManager.getRecentAchievements();

  if (recentAchievements.length === 0) {
    recentList.innerHTML = '<div class="no-achievements">لا توجد إنجازات حديثة</div>';
    return;
  }

  recentList.innerHTML = recentAchievements.map(achievement => `
    <div class="recent-achievement">
      <div class="achievement-icon">${achievement.icon}</div>
      <div class="recent-achievement-details">
        <div class="recent-achievement-name">${achievement.name}</div>
        <div class="recent-achievement-points">+${achievement.points} نقطة</div>
      </div>
    </div>
  `).join('');
}

// إعداد مرشحات الإنجازات
function setupAchievementFilters() {
  const filterBtns = document.querySelectorAll('.filter-btn');

  filterBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      // إزالة التحديد السابق
      filterBtns.forEach(b => b.classList.remove('active'));

      // تحديد الزر الجديد
      btn.classList.add('active');

      // تطبيق المرشح
      const filter = btn.dataset.filter;
      filterAchievements(filter);
    });
  });
}

// تطبيق مرشح الإنجازات
function filterAchievements(filter) {
  const achievementCards = document.querySelectorAll('.achievement-card');

  achievementCards.forEach(card => {
    const isUnlocked = card.classList.contains('unlocked');

    switch (filter) {
      case 'all':
        card.style.display = 'block';
        break;
      case 'unlocked':
        card.style.display = isUnlocked ? 'block' : 'none';
        break;
      case 'locked':
        card.style.display = !isUnlocked ? 'block' : 'none';
        break;
    }
  });
}

// عرض الأذكار في حاوية محددة
function displayAzkarInContainer(containerId, azkarList, category) {
  const container = document.getElementById(containerId);
  if (!container) return;

  container.innerHTML = azkarList.map(zikr => {
    const zikrElement = createZikrElement(zikr, category);
    return zikrElement.outerHTML;
  }).join('');

  // إضافة مستمعي الأحداث للعدادات
  container.querySelectorAll('.counter-display').forEach(display => {
    display.addEventListener('click', () => {
      const maxCount = parseInt(display.dataset.maxCount);
      incrementCounter(display, maxCount);

      // تسجيل قراءة الذكر للإنجازات
      if (window.achievementsManager) {
        achievementsManager.recordAzkarRead(category);
      }

      // تحديث إحصائيات رمضان إذا كان ذكر رمضان
      if (category.startsWith('ramadan-')) {
        const ramadanType = category.replace('ramadan-', '');
        ramadanAzkarManager.updateRamadanStats(ramadanType);
      }
    });
  });
}

// تحديث العداد مع ربطه بنظام الإنجازات
function incrementCounter(display, maxCount) {
  const currentText = display.textContent;
  const currentCount = parseInt(currentText.split(' / ')[0]);

  if (currentCount < maxCount) {
    const newCount = currentCount + 1;
    display.textContent = `${newCount} / ${maxCount}`;

    if (newCount === maxCount) {
      display.style.color = '#4CAF50';
      display.style.fontWeight = 'bold';

      // إضافة تأثير بصري للإكمال
      display.parentElement.classList.add('completed');
      setTimeout(() => {
        display.parentElement.classList.remove('completed');
      }, 2000);
    }
  }
}

// وظيفة مشاركة الذكر
function shareZikr(zikrText, translation = '') {
  if (window.shareManager) {
    shareManager.showShareDialog(zikrText, translation);
  } else {
    // نسخ النص كبديل
    const textToShare = `${zikrText}\n\n${translation ? translation + '\n\n' : ''}من تطبيق أذكار المسلم`;
    navigator.clipboard.writeText(textToShare).then(() => {
      showSuccessMessage('تم نسخ النص إلى الحافظة');
    }).catch(() => {
      console.error('فشل في نسخ النص');
    });
  }
}

// تهيئة دعم اللغات
function initLanguageSupport() {
  if (!window.languageManager) {
    console.warn('مدير اللغات غير متاح');
    return;
  }

  // إعداد اختيار اللغة
  const languageSelector = document.getElementById('language-selector');
  if (languageSelector) {
    // تحديد اللغة الحالية
    languageSelector.value = languageManager.getCurrentLanguage();

    // إضافة مستمع الأحداث
    languageSelector.addEventListener('change', async (e) => {
      const newLanguage = e.target.value;
      const success = await languageManager.changeLanguage(newLanguage);

      if (success) {
        showSuccessMessage('تم تغيير اللغة بنجاح');
        // إعادة تحميل الصفحة لتطبيق التغييرات
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        showErrorMessage('فشل في تغيير اللغة');
        languageSelector.value = languageManager.getCurrentLanguage();
      }
    });
  }

  // تطبيق الترجمات الحالية
  languageManager.updatePageLanguage();
}

// تهيئة نظام النسخ الاحتياطي
function initBackupSystem() {
  if (!window.cloudBackupManager) {
    console.warn('مدير النسخ الاحتياطي غير متاح');
    return;
  }

  // إضافة واجهة النسخ الاحتياطي
  const backupContainer = document.getElementById('backup-container');
  if (backupContainer) {
    const backupInterface = cloudBackupManager.createBackupInterface();
    backupContainer.appendChild(backupInterface);
  }

  // إنشاء نسخة احتياطية تلقائية
  cloudBackupManager.createAutoBackup();
}

// عرض رسالة نجاح
function showSuccessMessage(message) {
  const successDiv = document.createElement('div');
  successDiv.className = 'success-message';
  successDiv.textContent = message;

  document.body.appendChild(successDiv);

  setTimeout(() => {
    if (successDiv.parentNode) {
      successDiv.parentNode.removeChild(successDiv);
    }
  }, 3000);
}

// عرض رسالة خطأ
function showErrorMessage(message) {
  const errorDiv = document.createElement('div');
  errorDiv.className = 'audio-error-message';
  errorDiv.textContent = message;

  document.body.appendChild(errorDiv);

  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 3000);
}
