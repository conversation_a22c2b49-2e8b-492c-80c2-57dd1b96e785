// متغيرات الصوت العامة
let currentAudio = null;
let audioEnabled = true;

document.addEventListener('DOMContentLoaded', function() {
  // تحميل الإعدادات
  loadSettings();

  // عرض الوقت الحالي
  updateCurrentTime();
  setInterval(updateCurrentTime, 1000);

  // عرض معلومات الذكر القادم
  updateNextAzkar();

  // تحميل الأذكار
  loadAzkar();

  // إعداد التبويبات
  setupTabs();

  // إعداد زر حفظ الإعدادات
  document.getElementById('save-settings').addEventListener('click', saveSettings);

  // تحميل إعدادات الصوت
  loadAudioSettings();

  // تهيئة المميزات المحسنة
  if (typeof initEnhancedFeatures === 'function') {
    initEnhancedFeatures();
  }
});

// تحديث الوقت الحالي
function updateCurrentTime() {
  const now = new Date();
  const timeString = now.toLocaleTimeString('ar-SA');
  document.getElementById('current-time').textContent = timeString;
}

// التحقق من صحة الوقت
function validateTime(timeString) {
  if (!timeString || typeof timeString !== 'string') {
    return false;
  }

  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  if (!timeRegex.test(timeString)) {
    return false;
  }

  const [hours, minutes] = timeString.split(':').map(Number);
  return hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59;
}

// تحميل الإعدادات
function loadSettings() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || {
      morning: "05:00",
      evening: "17:00",
      afterPrayer: true
    };
    
    document.getElementById('morning-time').value = times.morning;
    document.getElementById('evening-time').value = times.evening;
    document.getElementById('after-prayer').checked = times.afterPrayer;
  });
}

// حفظ الإعدادات
function saveSettings() {
  try {
    const morningTimeElement = document.getElementById('morning-time');
    const eveningTimeElement = document.getElementById('evening-time');
    const afterPrayerElement = document.getElementById('after-prayer');

    if (!morningTimeElement || !eveningTimeElement || !afterPrayerElement) {
      alert('خطأ: لا يمكن العثور على عناصر الإعدادات');
      return;
    }

    const morningTime = morningTimeElement.value;
    const eveningTime = eveningTimeElement.value;
    const afterPrayer = afterPrayerElement.checked;

    // التحقق من صحة الأوقات
    if (!morningTime || !eveningTime) {
      alert('يرجى تحديد أوقات صحيحة للأذكار');
      return;
    }

    // التحقق من صحة تنسيق الوقت
    if (!validateTime(morningTime) || !validateTime(eveningTime)) {
      alert('يرجى إدخال أوقات صحيحة (00:00 - 23:59)');
      return;
    }

    // التحقق من أن وقت المساء مختلف عن وقت الصباح
    if (morningTime === eveningTime) {
      alert('يجب أن يكون وقت أذكار المساء مختلفاً عن وقت أذكار الصباح');
      return;
    }

    // إعدادات الصوت
    const audioEnabledElement = document.getElementById('audio-enabled');
    const audioVolumeElement = document.getElementById('audio-volume');

    const settings = {
      morning: morningTime,
      evening: eveningTime,
      afterPrayer: afterPrayer
    };

    const audioSettings = {
      enabled: audioEnabledElement ? audioEnabledElement.checked : true,
      volume: audioVolumeElement ? parseFloat(audioVolumeElement.value) : 0.7
    };

    // تحديث المتغير العام
    audioEnabled = audioSettings.enabled;

    // حفظ الإعدادات
    chrome.storage.local.set({
      azkarTimes: settings,
      audioSettings: audioSettings
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('خطأ في حفظ الإعدادات:', chrome.runtime.lastError);
        alert('حدث خطأ في حفظ الإعدادات. يرجى المحاولة مرة أخرى.');
      } else {
        alert('تم حفظ الإعدادات بنجاح');
        updateNextAzkar();
      }
    });
  } catch (error) {
    console.error('خطأ في حفظ الإعدادات:', error);
    alert('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
  }
}

// تحديث معلومات الذكر القادم
function updateNextAzkar() {
  chrome.storage.local.get(['azkarTimes'], (result) => {
    const times = result.azkarTimes || {
      morning: "05:00",
      evening: "17:00"
    };
    
    const now = new Date();
    
    const [morningHours, morningMinutes] = times.morning.split(':').map(Number);
    const [eveningHours, eveningMinutes] = times.evening.split(':').map(Number);
    
    let morningTime = new Date();
    morningTime.setHours(morningHours, morningMinutes, 0);
    
    let eveningTime = new Date();
    eveningTime.setHours(eveningHours, eveningMinutes, 0);
    
    // إذا كان الوقت الحالي بعد وقت الصباح، نضبط وقت الصباح ليوم غد
    if (now > morningTime) {
      morningTime.setDate(morningTime.getDate() + 1);
    }
    
    // إذا كان الوقت الحالي بعد وقت المساء، نضبط وقت المساء ليوم غد
    if (now > eveningTime) {
      eveningTime.setDate(eveningTime.getDate() + 1);
    }
    
    // تحديد الذكر القادم (الأقرب زمنياً)
    let nextAzkarType, nextAzkarTime;
    
    if (morningTime < eveningTime) {
      nextAzkarType = "أذكار الصباح";
      nextAzkarTime = morningTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    } else {
      nextAzkarType = "أذكار المساء";
      nextAzkarTime = eveningTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
    }
    
    document.getElementById('next-azkar-type').textContent = nextAzkarType;
    document.getElementById('next-azkar-time').textContent = nextAzkarTime;
  });
}

// تحميل الأذكار
function loadAzkar() {
  try {
    // أذكار الصباح
    const morningContainer = document.getElementById('morning-azkar');
    if (morningContainer) {
      if (typeof morningAzkar !== 'undefined' && Array.isArray(morningAzkar)) {
        morningAzkar.forEach((zikr) => {
          morningContainer.appendChild(createZikrElement(zikr, 'morning'));
        });
      } else {
        morningContainer.innerHTML = '<p class="error-message">فشل في تحميل أذكار الصباح. يرجى إعادة تحميل الإضافة.</p>';
      }
    }

    // أذكار المساء
    const eveningContainer = document.getElementById('evening-azkar');
    if (eveningContainer) {
      if (typeof eveningAzkar !== 'undefined' && Array.isArray(eveningAzkar)) {
        eveningAzkar.forEach((zikr) => {
          eveningContainer.appendChild(createZikrElement(zikr, 'evening'));
        });
      } else {
        eveningContainer.innerHTML = '<p class="error-message">فشل في تحميل أذكار المساء. يرجى إعادة تحميل الإضافة.</p>';
      }
    }

    // أذكار بعد الصلاة
    const prayerContainer = document.getElementById('prayer-azkar');
    if (prayerContainer) {
      if (typeof afterPrayerAzkar !== 'undefined' && Array.isArray(afterPrayerAzkar)) {
        afterPrayerAzkar.forEach((zikr) => {
          prayerContainer.appendChild(createZikrElement(zikr, 'prayer'));
        });
      } else {
        prayerContainer.innerHTML = '<p class="error-message">فشل في تحميل أذكار بعد الصلاة. يرجى إعادة تحميل الإضافة.</p>';
      }
    }
  } catch (error) {
    console.error('خطأ في تحميل الأذكار:', error);
    alert('حدث خطأ في تحميل الأذكار. يرجى إعادة تحميل الإضافة.');
  }
}

// إنشاء عنصر ذكر
function createZikrElement(zikr, category = 'morning') {
  const zikrElement = document.createElement('div');
  zikrElement.className = 'zikr-item';

  // إضافة أزرار التحكم في الصوت
  const audioControls = document.createElement('div');
  audioControls.className = 'audio-controls';

  const audioPath = getAudioPath(zikr.text, category);
  if (audioPath) {
    const playButton = document.createElement('button');
    playButton.className = 'audio-btn play-btn';
    playButton.innerHTML = '🔊';
    playButton.title = 'تشغيل التلاوة';
    playButton.onclick = () => playAudio(audioPath);

    const stopButton = document.createElement('button');
    stopButton.className = 'audio-btn stop-btn';
    stopButton.innerHTML = '⏹️';
    stopButton.title = 'إيقاف التلاوة';
    stopButton.onclick = () => stopAudio();

    audioControls.appendChild(playButton);
    audioControls.appendChild(stopButton);
  }

  const zikrText = document.createElement('div');
  zikrText.className = 'zikr-text';
  zikrText.textContent = zikr.text;

  const zikrTranslation = document.createElement('div');
  zikrTranslation.className = 'zikr-translation';
  zikrTranslation.textContent = zikr.translation || '';

  const zikrCountContainer = document.createElement('div');
  zikrCountContainer.className = 'zikr-count-container';

  const zikrCount = document.createElement('div');
  zikrCount.className = 'zikr-count';
  zikrCount.textContent = `عدد المرات: ${zikr.count}`;

  // إضافة عداد تفاعلي إذا كان العدد أكثر من 1
  if (zikr.count > 1) {
    const counterContainer = document.createElement('div');
    counterContainer.className = 'counter-container';

    const counterDisplay = document.createElement('span');
    counterDisplay.className = 'counter-display';
    counterDisplay.textContent = `0 / ${zikr.count}`;

    const incrementButton = document.createElement('button');
    incrementButton.className = 'counter-btn';
    incrementButton.textContent = '+';
    incrementButton.onclick = () => incrementCounter(counterDisplay, zikr.count);

    const resetButton = document.createElement('button');
    resetButton.className = 'counter-btn reset-btn';
    resetButton.textContent = 'إعادة تعيين';
    resetButton.onclick = () => resetCounter(counterDisplay, zikr.count);

    counterContainer.appendChild(counterDisplay);
    counterContainer.appendChild(incrementButton);
    counterContainer.appendChild(resetButton);
    zikrCountContainer.appendChild(counterContainer);
  }

  // ترتيب العناصر
  zikrElement.appendChild(audioControls);
  zikrElement.appendChild(zikrText);
  if (zikr.translation) {
    zikrElement.appendChild(zikrTranslation);
  }
  zikrElement.appendChild(zikrCount);
  zikrElement.appendChild(zikrCountContainer);

  return zikrElement;
}

// زيادة العداد
function incrementCounter(display, maxCount) {
  const currentText = display.textContent;
  const currentCount = parseInt(currentText.split(' / ')[0]);

  if (currentCount < maxCount) {
    const newCount = currentCount + 1;
    display.textContent = `${newCount} / ${maxCount}`;

    if (newCount === maxCount) {
      display.style.color = '#4CAF50';
      display.style.fontWeight = 'bold';
    }
  }
}

// إعادة تعيين العداد
function resetCounter(display, maxCount) {
  display.textContent = `0 / ${maxCount}`;
  display.style.color = '';
  display.style.fontWeight = '';
}

// وظائف الصوت
function loadAudioSettings() {
  chrome.storage.local.get(['audioSettings'], (result) => {
    const settings = result.audioSettings || { enabled: true, volume: 0.7 };
    audioEnabled = settings.enabled;

    // إضافة عنصر تحكم الصوت في الإعدادات إذا لم يكن موجود
    addAudioControlsToSettings();
  });
}

function addAudioControlsToSettings() {
  const settingsForm = document.querySelector('.settings-form');
  if (settingsForm && !document.getElementById('audio-enabled')) {
    const audioGroup = document.createElement('div');
    audioGroup.className = 'form-group';
    audioGroup.innerHTML = `
      <label for="audio-enabled">تفعيل التلاوة الصوتية:</label>
      <input type="checkbox" id="audio-enabled" name="audio-enabled" ${audioEnabled ? 'checked' : ''}>
    `;

    const volumeGroup = document.createElement('div');
    volumeGroup.className = 'form-group';
    volumeGroup.innerHTML = `
      <label for="audio-volume">مستوى الصوت:</label>
      <input type="range" id="audio-volume" name="audio-volume" min="0" max="1" step="0.1" value="0.7">
    `;

    settingsForm.insertBefore(audioGroup, settingsForm.lastElementChild);
    settingsForm.insertBefore(volumeGroup, settingsForm.lastElementChild);
  }
}

function playAudio(audioPath) {
  if (!audioEnabled) return;

  try {
    // إيقاف الصوت الحالي إذا كان يعمل
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
    }

    // تشغيل الصوت الجديد
    currentAudio = new Audio(chrome.runtime.getURL(audioPath));
    currentAudio.volume = document.getElementById('audio-volume')?.value || 0.7;

    currentAudio.play().catch(error => {
      console.log('لا يمكن تشغيل الملف الصوتي:', error);
    });

  } catch (error) {
    console.error('خطأ في تشغيل الصوت:', error);
  }
}

function stopAudio() {
  if (currentAudio) {
    currentAudio.pause();
    currentAudio.currentTime = 0;
    currentAudio = null;
  }
}

function getAudioPath(zikrText, category) {
  // تحديد مسار الملف الصوتي بناءً على نص الذكر والفئة
  const audioMap = {
    'آية الكرسي': 'audio/' + category + '/ayat-kursi.mp3',
    'سورة الإخلاص': 'audio/' + category + '/ikhlas.mp3',
    'سورة الفلق': 'audio/' + category + '/falaq.mp3',
    'سورة الناس': 'audio/' + category + '/nas.mp3',
    'سبحان الله': 'audio/prayer/tasbih.mp3',
    'الحمد لله': 'audio/prayer/tahmid.mp3',
    'الله أكبر': 'audio/prayer/takbir.mp3'
  };

  // البحث عن تطابق في النص
  for (const [key, path] of Object.entries(audioMap)) {
    if (zikrText.includes(key)) {
      return path;
    }
  }

  return null; // لا يوجد ملف صوتي متاح
}

// إعداد التبويبات
function setupTabs() {
  const tabs = document.querySelectorAll('.tab');
  const tabContents = document.querySelectorAll('.tab-content');
  
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // إزالة الفئة النشطة من جميع التبويبات
      tabs.forEach(t => t.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));
      
      // إضافة الفئة النشطة للتبويب المحدد
      tab.classList.add('active');
      const tabId = tab.getAttribute('data-tab');
      document.getElementById(tabId).classList.add('active');
    });
  });
}
