@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  direction: rtl;
}

.container {
  width: 400px;
  min-height: 500px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header {
  background-color: #4CAF50;
  color: white;
  padding: 15px;
  text-align: center;
}

h1 {
  font-size: 24px;
  margin: 0;
}

h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #4CAF50;
}

.tabs {
  display: flex;
  list-style: none;
  background-color: #f1f1f1;
  overflow-x: auto;
}

.tab {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
}

.tab:hover {
  background-color: #ddd;
}

.tab.active {
  background-color: #4CAF50;
  color: white;
}

.tab-content {
  display: none;
  padding: 20px;
}

.tab-content.active {
  display: block;
}

.welcome {
  text-align: center;
  padding: 20px 0;
}

.current-time, .next-azkar {
  margin: 15px 0;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.azkar-list {
  margin-top: 15px;
}

.zikr-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
  border-right: 3px solid #4CAF50;
}

.zikr-text {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 10px;
}

.zikr-translation {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.zikr-count {
  font-size: 14px;
  color: #4CAF50;
  font-weight: bold;
}

.settings-form {
  max-width: 300px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

input[type="time"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn {
  display: inline-block;
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 16px;
}

.btn:hover {
  background-color: #45a049;
}

footer {
  text-align: center;
  padding: 10px;
  background-color: #f1f1f1;
  font-size: 12px;
  color: #666;
}

/* تنسيق العداد */
.zikr-count-container {
  margin-top: 10px;
}

.counter-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
}

.counter-display {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  min-width: 60px;
}

.counter-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.counter-btn:hover {
  background-color: #45a049;
}

.reset-btn {
  background-color: #f44336;
}

.reset-btn:hover {
  background-color: #da190b;
}

/* تحسينات إضافية */
.zikr-item:hover {
  background-color: #f0f0f0;
  transition: background-color 0.3s;
}

.notification-settings {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.form-group input[type="checkbox"] {
  margin-left: 10px;
  transform: scale(1.2);
}

/* تنسيق متجاوب */
@media (max-width: 450px) {
  .container {
    width: 100%;
  }

  .tabs {
    font-size: 12px;
  }

  .tab {
    padding: 8px 10px;
  }
}
