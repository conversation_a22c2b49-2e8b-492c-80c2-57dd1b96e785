/* خطوط عربية آمنة للإضافات */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

/* خطوط احتياطية محلية */
@font-face {
  font-family: 'Arabic-Safe';
  src: local('Tahoma'), local('Arial Unicode MS'), local('Segoe UI');
  font-display: swap;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Noto Sans Arabic', 'Arabic-Safe', 'Tahoma', 'Arial Unicode MS', 'Segoe UI', Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  direction: rtl;
  font-size: 14px;
  line-height: 1.6;
}

.container {
  width: 400px;
  min-height: 500px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header {
  background-color: #4CAF50;
  color: white;
  padding: 15px;
  text-align: center;
}

h1 {
  font-size: 24px;
  margin: 0;
}

h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #4CAF50;
}

.tabs {
  display: flex;
  list-style: none;
  background-color: #f1f1f1;
  overflow-x: auto;
}

.tab {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
}

.tab:hover {
  background-color: #ddd;
}

.tab.active {
  background-color: #4CAF50;
  color: white;
}

.tab-content {
  display: none;
  padding: 20px;
}

.tab-content.active {
  display: block;
}

.welcome {
  text-align: center;
  padding: 20px 0;
}

.current-time, .next-azkar {
  margin: 15px 0;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.azkar-list {
  margin-top: 15px;
}

.zikr-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
  border-right: 3px solid #4CAF50;
}

.zikr-text {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 10px;
}

.zikr-translation {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.zikr-count {
  font-size: 14px;
  color: #4CAF50;
  font-weight: bold;
}

.settings-form {
  max-width: 300px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

input[type="time"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn {
  display: inline-block;
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 16px;
}

.btn:hover {
  background-color: #45a049;
}

footer {
  text-align: center;
  padding: 10px;
  background-color: #f1f1f1;
  font-size: 12px;
  color: #666;
}

/* تنسيق العداد */
.zikr-count-container {
  margin-top: 10px;
}

.counter-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
}

.counter-display {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  min-width: 60px;
}

.counter-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.counter-btn:hover {
  background-color: #45a049;
}

.reset-btn {
  background-color: #f44336;
}

.reset-btn:hover {
  background-color: #da190b;
}

/* تحسينات إضافية */
.zikr-item:hover {
  background-color: #f0f0f0;
  transition: background-color 0.3s;
}

.notification-settings {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.form-group input[type="checkbox"] {
  margin-left: 10px;
  transform: scale(1.2);
}

/* رسائل الخطأ */
.error-message {
  color: #f44336;
  background-color: #ffebee;
  padding: 15px;
  border-radius: 5px;
  border-right: 3px solid #f44336;
  margin: 10px 0;
  font-weight: bold;
  text-align: center;
}

.success-message {
  color: #4CAF50;
  background-color: #e8f5e8;
  padding: 15px;
  border-radius: 5px;
  border-right: 3px solid #4CAF50;
  margin: 10px 0;
  font-weight: bold;
  text-align: center;
}

/* تنسيق الأزرار الصوتية */
.audio-controls {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;
  justify-content: flex-end;
}

.audio-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.audio-btn:hover {
  background-color: rgba(76, 175, 80, 0.1);
  transform: scale(1.1);
}

.play-btn:hover {
  background-color: rgba(76, 175, 80, 0.2);
}

.stop-btn:hover {
  background-color: rgba(244, 67, 54, 0.2);
}

.audio-btn:active {
  transform: scale(0.95);
}

/* تنسيق عناصر التحكم في الصوت في الإعدادات */
input[type="range"] {
  width: 100%;
  margin: 10px 0;
  -webkit-appearance: none;
  appearance: none;
  height: 5px;
  border-radius: 5px;
  background: #ddd;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4CAF50;
  cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4CAF50;
  cursor: pointer;
  border: none;
}

/* تحسينات إضافية للخط العربي */
.zikr-text {
  font-family: 'Noto Sans Arabic', 'Arabic-Safe', 'Tahoma', 'Arial Unicode MS', sans-serif;
  font-weight: 400;
  line-height: 1.8;
  text-align: justify;
}

.zikr-translation {
  font-family: 'Noto Sans Arabic', 'Arabic-Safe', 'Tahoma', sans-serif;
  font-style: italic;
}

/* تنسيق متجاوب */
@media (max-width: 450px) {
  .container {
    width: 100%;
  }

  .tabs {
    font-size: 12px;
  }

  .tab {
    padding: 8px 10px;
  }

  .audio-controls {
    justify-content: center;
  }

  .audio-btn {
    font-size: 16px;
    width: 30px;
    height: 30px;
  }
}

/* الوضع الليلي */
.dark-theme {
  background-color: #1a1a1a;
  color: #e0e0e0;
}

.dark-theme .container {
  background-color: #2d2d2d;
  color: #e0e0e0;
}

.dark-theme header {
  background-color: #1976D2;
}

.dark-theme .tabs {
  background-color: #333;
}

.dark-theme .tab {
  color: #e0e0e0;
}

.dark-theme .tab:hover {
  background-color: #555;
}

.dark-theme .tab.active {
  background-color: #1976D2;
}

.dark-theme .zikr-item {
  background-color: #333;
  border-right-color: #1976D2;
}

.dark-theme .current-time,
.dark-theme .next-azkar {
  background-color: #333;
}

.dark-theme input[type="time"],
.dark-theme input[type="range"] {
  background-color: #444;
  color: #e0e0e0;
  border-color: #555;
}

/* زر تبديل الثيم */
.theme-toggle {
  position: absolute;
  top: 15px;
  left: 15px;
}

.theme-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.theme-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

/* إحصائيات */
.stats-display {
  margin: 20px 0;
  padding: 15px;
  background-color: #e8f5e8;
  border-radius: 8px;
  border-right: 4px solid #4CAF50;
}

.dark-theme .stats-display {
  background-color: #1a4a1a;
  border-right-color: #66BB6A;
}

.stats-display h3 {
  margin-bottom: 10px;
  color: #2E7D32;
}

.dark-theme .stats-display h3 {
  color: #81C784;
}

.stat-number {
  font-weight: bold;
  font-size: 18px;
  color: #4CAF50;
}

.dark-theme .stat-number {
  color: #81C784;
}

/* تأثير الإكمال */
.completed {
  animation: completionGlow 2s ease-in-out;
}

@keyframes completionGlow {
  0% { background-color: inherit; }
  50% { background-color: rgba(76, 175, 80, 0.3); }
  100% { background-color: inherit; }
}

/* عناصر النسخ الاحتياطي */
.backup-controls {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.dark-theme .backup-controls {
  background-color: #333;
}

.backup-controls h3 {
  margin-bottom: 15px;
  color: #666;
}

.dark-theme .backup-controls h3 {
  color: #ccc;
}

.btn.secondary {
  background-color: #757575;
  margin: 5px;
}

.btn.secondary:hover {
  background-color: #616161;
}
