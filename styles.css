/* خطوط عربية آمنة للإضافات */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

/* خطوط احتياطية محلية */
@font-face {
  font-family: 'Arabic-Safe';
  src: local('Tahoma'), local('Arial Unicode MS'), local('Segoe UI');
  font-display: swap;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Noto Sans Arabic', 'Arabic-Safe', 'Tahoma', 'Arial Unicode MS', 'Segoe UI', Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  direction: rtl;
  font-size: 14px;
  line-height: 1.6;
  padding: 0;
  margin: 0;
  min-width: 350px;
  text-align: right;
}

.container {
  width: 100%;
  max-width: 450px;
  min-height: 500px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  border-radius: 8px;
}

header {
  background-color: #4CAF50;
  color: white;
  padding: 15px;
  text-align: center;
}

h1 {
  font-size: 24px;
  margin: 0;
}

h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #4CAF50;
}

.tabs {
  display: flex;
  list-style: none;
  background-color: #f1f1f1;
  overflow-x: auto;
}

.tab {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
}

.tab:hover {
  background-color: #ddd;
}

.tab.active {
  background-color: #4CAF50;
  color: white;
}

.tab-content {
  display: none;
  padding: 20px;
}

.tab-content.active {
  display: block;
}

.welcome {
  text-align: center;
  padding: 20px 0;
}

.current-time, .next-azkar {
  margin: 15px 0;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.azkar-list {
  margin-top: 15px;
}

.zikr-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
  border-right: 3px solid #4CAF50;
}

.zikr-text {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 10px;
}

.zikr-translation {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.zikr-count {
  font-size: 14px;
  color: #4CAF50;
  font-weight: bold;
}

.settings-form {
  max-width: 300px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

input[type="time"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn {
  display: inline-block;
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 16px;
}

.btn:hover {
  background-color: #45a049;
}

.btn-primary {
  background-color: #4CAF50;
  color: white;
  border: 1px solid #4CAF50;
}

.btn-primary:hover {
  background-color: #45a049;
  border-color: #45a049;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  border: 1px solid #6c757d;
}

.btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

footer {
  text-align: center;
  padding: 10px;
  background-color: #f1f1f1;
  font-size: 12px;
  color: #666;
}

/* تنسيق العداد */
.zikr-count-container {
  margin-top: 10px;
}

.counter-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
}

.counter-display {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  min-width: 60px;
}

.counter-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.counter-btn:hover {
  background-color: #45a049;
}

.reset-btn {
  background-color: #f44336;
}

.reset-btn:hover {
  background-color: #da190b;
}

/* تحسينات إضافية */
.zikr-item:hover {
  background-color: #f0f0f0;
  transition: background-color 0.3s;
}

.notification-settings {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.form-group input[type="checkbox"] {
  margin-left: 10px;
  transform: scale(1.2);
}

/* رسائل الخطأ */
.error-message {
  color: #f44336;
  background-color: #ffebee;
  padding: 15px;
  border-radius: 5px;
  border-right: 3px solid #f44336;
  margin: 10px 0;
  font-weight: bold;
  text-align: center;
}

.success-message {
  color: #4CAF50;
  background-color: #e8f5e8;
  padding: 15px;
  border-radius: 5px;
  border-right: 3px solid #4CAF50;
  margin: 10px 0;
  font-weight: bold;
  text-align: center;
}

/* تنسيق الأزرار الصوتية */
.audio-controls {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;
  justify-content: flex-end;
}

.audio-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.audio-btn:hover {
  background-color: rgba(76, 175, 80, 0.1);
  transform: scale(1.1);
}

.play-btn:hover {
  background-color: rgba(76, 175, 80, 0.2);
}

.stop-btn:hover {
  background-color: rgba(244, 67, 54, 0.2);
}

.audio-btn:active {
  transform: scale(0.95);
}

/* تنسيق عناصر التحكم في الصوت في الإعدادات */
input[type="range"] {
  width: 100%;
  margin: 10px 0;
  -webkit-appearance: none;
  appearance: none;
  height: 5px;
  border-radius: 5px;
  background: #ddd;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4CAF50;
  cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4CAF50;
  cursor: pointer;
  border: none;
}

/* تحسينات إضافية للخط العربي */
.zikr-text {
  font-family: 'Noto Sans Arabic', 'Arabic-Safe', 'Tahoma', 'Arial Unicode MS', sans-serif;
  font-weight: 400;
  line-height: 1.8;
  text-align: justify;
}

.zikr-translation {
  font-family: 'Noto Sans Arabic', 'Arabic-Safe', 'Tahoma', sans-serif;
  font-style: italic;
}

/* تنسيق متجاوب */
@media (max-width: 450px) {
  body {
    padding: 5px;
  }

  .container {
    width: 100%;
    max-width: none;
    margin: 0;
    border-radius: 4px;
  }

  .tabs {
    font-size: 12px;
  }

  .tab {
    padding: 8px 10px;
  }

  .tab-content {
    padding: 15px;
  }

  .audio-controls {
    justify-content: center;
  }

  .audio-btn {
    font-size: 16px;
    width: 30px;
    height: 30px;
  }

  .zikr-item {
    padding: 12px;
    margin-bottom: 15px;
  }
}

/* الوضع الليلي - تحسينات التباين */
.dark-theme {
  background-color: #121212;
  color: #ffffff;
}

.dark-theme .container {
  background-color: #1e1e1e;
  color: #ffffff;
  border: 1px solid #333333;
}

/* تحسين وضوح النصوص في الوضع الليلي */
.dark-theme .dhikr-text,
.dark-theme .dhikr-arabic,
.dark-theme .dhikr-translation {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.dark-theme .dhikr-source {
  color: #b0b0b0 !important;
}

.dark-theme .dhikr-count {
  color: #81c784 !important;
  font-weight: bold;
}

.dark-theme header {
  background-color: #1976D2;
}

.dark-theme .tabs {
  background-color: #333;
}

.dark-theme .tab {
  color: #e0e0e0;
}

.dark-theme .tab:hover {
  background-color: #555;
}

.dark-theme .tab.active {
  background-color: #1976D2;
}

.dark-theme .zikr-item {
  background-color: #2d2d2d;
  border-right-color: #1976D2;
  border: 1px solid #404040;
}

.dark-theme .zikr-item:hover {
  background-color: #353535;
  border-color: #505050;
}

.dark-theme .current-time,
.dark-theme .next-azkar {
  background-color: #333;
}

.dark-theme input[type="time"],
.dark-theme input[type="range"],
.dark-theme input[type="text"],
.dark-theme input[type="number"],
.dark-theme select {
  background-color: #2d2d2d;
  color: #ffffff;
  border: 1px solid #555555;
}

.dark-theme input[type="time"]:focus,
.dark-theme input[type="range"]:focus,
.dark-theme input[type="text"]:focus,
.dark-theme input[type="number"]:focus,
.dark-theme select:focus {
  border-color: #1976D2;
  box-shadow: 0 0 5px rgba(25, 118, 210, 0.3);
}

/* تحسين وضوح النصوص في الأزرار */
.dark-theme button {
  background-color: #1976D2;
  color: #ffffff;
  border: 1px solid #1565C0;
}

.dark-theme button:hover {
  background-color: #1565C0;
  border-color: #0D47A1;
}

.dark-theme .btn-secondary {
  background-color: #424242;
  color: #ffffff;
  border-color: #616161;
}

.dark-theme .btn-secondary:hover {
  background-color: #616161;
  border-color: #757575;
}

/* زر تبديل الثيم */
.theme-toggle {
  position: absolute;
  top: 15px;
  left: 15px;
}

.theme-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.theme-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

/* إحصائيات */
.stats-display {
  margin: 20px 0;
  padding: 15px;
  background-color: #e8f5e8;
  border-radius: 8px;
  border-right: 4px solid #4CAF50;
}

.dark-theme .stats-display {
  background-color: #1a4a1a;
  border-right-color: #66BB6A;
}

.stats-display h3 {
  margin-bottom: 10px;
  color: #2E7D32;
}

.dark-theme .stats-display h3 {
  color: #81C784;
}

.stat-number {
  font-weight: bold;
  font-size: 18px;
  color: #4CAF50;
}

.dark-theme .stat-number {
  color: #81C784;
}

/* تأثير الإكمال */
.completed {
  animation: completionGlow 2s ease-in-out;
}

@keyframes completionGlow {
  0% { background-color: inherit; }
  50% { background-color: rgba(76, 175, 80, 0.3); }
  100% { background-color: inherit; }
}

/* عناصر النسخ الاحتياطي */
.backup-controls {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.dark-theme .backup-controls {
  background-color: #333;
}

.backup-controls h3 {
  margin-bottom: 15px;
  color: #666;
}

.dark-theme .backup-controls h3 {
  color: #ccc;
}

.btn.secondary {
  background-color: #757575;
  margin: 5px;
}

.btn.secondary:hover {
  background-color: #616161;
}

/* واجهة التحكم في الصوت المتقدمة */
.audio-controls-advanced {
  margin: 15px 0;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.dark-theme .audio-controls-advanced {
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  border-color: #4a5568;
}

.audio-player {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-start;
}

.audio-btn {
  background: #4CAF50;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.audio-btn:hover {
  background: #45a049;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.audio-btn:active {
  transform: translateY(0);
}

.audio-btn.stop-btn {
  background: #f44336;
}

.audio-btn.stop-btn:hover {
  background: #da190b;
}

.audio-btn.repeat-btn.active {
  background: #FF9800;
}

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-bar {
  position: relative;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  cursor: pointer;
  overflow: hidden;
}

.dark-theme .progress-bar {
  background: #4a5568;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%);
  border-radius: 3px;
  transition: width 0.1s ease;
  width: 0%;
}

.progress-handle {
  position: absolute;
  top: 50%;
  right: 0;
  width: 12px;
  height: 12px;
  background: #4CAF50;
  border-radius: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progress-bar:hover .progress-handle {
  opacity: 1;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  font-family: 'Courier New', monospace;
}

.dark-theme .time-display {
  color: #a0aec0;
}

.volume-container,
.speed-container,
.reader-selector-container {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: #4CAF50;
  border-radius: 50%;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #4CAF50;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.speed-selector,
.reader-selector {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 12px;
  min-width: 80px;
}

.dark-theme .speed-selector,
.dark-theme .reader-selector {
  background: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
}

.speed-label,
.reader-label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.dark-theme .speed-label,
.dark-theme .reader-label {
  color: #a0aec0;
}

.no-audio {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #999;
  font-style: italic;
}

.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  background: #ffebee;
  color: #c62828;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 8px;
  border-right: 3px solid #f44336;
}

.dark-theme .error-message {
  background: #4a1a1a;
  color: #ef5350;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 450px) {
  .audio-controls-advanced {
    padding: 10px;
  }

  .audio-player {
    gap: 8px;
  }

  .player-controls {
    justify-content: center;
  }

  .audio-btn {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }

  .volume-container,
  .speed-container,
  .reader-selector-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .volume-slider {
    width: 100%;
  }

  .speed-selector,
  .reader-selector {
    width: 100%;
  }
}

/* متغيرات CSS للثيمات */
:root {
  --color-primary: #4CAF50;
  --color-primary-dark: #388E3C;
  --color-primary-light: #81C784;
  --color-secondary: #2196F3;
  --color-background: #ffffff;
  --color-surface: #f5f5f5;
  --color-text: #333333;
  --color-text-secondary: #666666;
  --color-border: #e0e0e0;
  --color-success: #4CAF50;
  --color-warning: #FF9800;
  --color-error: #f44336;
}

/* ثيم المحيط */
.ocean {
  --color-primary: #2196F3;
  --color-primary-dark: #1976D2;
  --color-primary-light: #64B5F6;
  --color-secondary: #00BCD4;
  --color-background: #ffffff;
  --color-surface: #f3f8ff;
  --color-text: #1a237e;
  --color-text-secondary: #3f51b5;
  --color-border: #e3f2fd;
}

.ocean header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.ocean .tab.active {
  background-color: var(--color-primary);
}

.ocean .zikr-item {
  border-right-color: var(--color-primary);
}

/* ثيم الذهبي */
.golden {
  --color-primary: #FF9800;
  --color-primary-dark: #F57C00;
  --color-primary-light: #FFB74D;
  --color-secondary: #FFC107;
  --color-background: #ffffff;
  --color-surface: #fffbf0;
  --color-text: #bf360c;
  --color-text-secondary: #e65100;
  --color-border: #fff3e0;
}

.golden header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.golden .tab.active {
  background-color: var(--color-primary);
}

.golden .zikr-item {
  border-right-color: var(--color-primary);
  background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
}

/* ثيم البنفسجي */
.purple {
  --color-primary: #9C27B0;
  --color-primary-dark: #7B1FA2;
  --color-primary-light: #BA68C8;
  --color-secondary: #673AB7;
  --color-background: #ffffff;
  --color-surface: #faf5ff;
  --color-text: #4a148c;
  --color-text-secondary: #6a1b9a;
  --color-border: #f3e5f5;
}

.purple header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.purple .tab.active {
  background-color: var(--color-primary);
}

.purple .zikr-item {
  border-right-color: var(--color-primary);
}

/* ثيم الغابة */
.forest {
  --color-primary: #2E7D32;
  --color-primary-dark: #1B5E20;
  --color-primary-light: #4CAF50;
  --color-secondary: #388E3C;
  --color-background: #ffffff;
  --color-surface: #f1f8e9;
  --color-text: #1b5e20;
  --color-text-secondary: #2e7d32;
  --color-border: #e8f5e8;
}

.forest header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.forest .container {
  background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
}

/* ثيم الغروب */
.sunset {
  --color-primary: #FF5722;
  --color-primary-dark: #D84315;
  --color-primary-light: #FF8A65;
  --color-secondary: #FF9800;
  --color-background: #ffffff;
  --color-surface: #fff3e0;
  --color-text: #bf360c;
  --color-text-secondary: #d84315;
  --color-border: #ffccbc;
}

.sunset header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.sunset .zikr-item {
  border-right-color: var(--color-primary);
  background: linear-gradient(135deg, #fff3e0 0%, #ffccbc 100%);
}

/* ثيم رمضان */
.ramadan {
  --color-primary: #8E24AA;
  --color-primary-dark: #6A1B9A;
  --color-primary-light: #BA68C8;
  --color-secondary: #FFD700;
  --color-background: #ffffff;
  --color-surface: #faf5ff;
  --color-text: #4a148c;
  --color-text-secondary: #6a1b9a;
  --color-border: #e1bee7;
}

.ramadan header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.ramadan .zikr-item {
  border-right-color: var(--color-primary);
  background: linear-gradient(135deg, #faf5ff 0%, #f3e5f5 100%);
}

.ramadan .zikr-item::before {
  content: '🌙⭐';
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 12px;
  opacity: 0.3;
}

/* تطبيق المتغيرات على العناصر */
header {
  background-color: var(--color-primary);
}

.container {
  background-color: var(--color-background);
  color: var(--color-text);
}

.tab.active {
  background-color: var(--color-primary);
}

.tab:hover {
  background-color: var(--color-surface);
}

.zikr-item {
  background-color: var(--color-surface);
  border-right-color: var(--color-primary);
  color: var(--color-text);
}

.zikr-translation {
  color: var(--color-text-secondary);
}

.btn {
  background-color: var(--color-primary);
}

.btn:hover {
  background-color: var(--color-primary-dark);
}

.audio-btn {
  background: var(--color-primary);
}

.audio-btn:hover {
  background: var(--color-primary-dark);
}

.progress-fill {
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
}

/* انتقالات سلسة للثيمات */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* اختيار الثيم */
.theme-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  margin: 20px 0;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border: 2px solid var(--color-border);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--color-surface);
}

.theme-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.theme-option.active {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.theme-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.theme-name {
  font-size: 14px;
  font-weight: bold;
  color: var(--color-text);
  text-align: center;
}

.theme-description {
  font-size: 12px;
  color: var(--color-text-secondary);
  text-align: center;
  margin-top: 4px;
}

/* إعدادات حجم الخط */
.font-preview {
  margin: 15px 0;
  padding: 15px;
  background: var(--color-surface);
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

#font-preview-text {
  margin: 0;
  text-align: center;
  color: var(--color-text);
  transition: font-size 0.3s ease;
}

/* أحجام الخط */
.font-size-small {
  font-size: 14px;
}

.font-size-medium {
  font-size: 16px;
}

.font-size-large {
  font-size: 18px;
}

.font-size-extra-large {
  font-size: 22px;
}

/* تطبيق أحجام الخط على النصوص */
.font-size-small .dhikr-text,
.font-size-small .dhikr-arabic,
.font-size-small .dhikr-translation,
.font-size-small .prayer-name,
.font-size-small .prayer-time,
.font-size-small .dhikr-count,
.font-size-small .dhikr-source {
  font-size: 14px !important;
}

.font-size-medium .dhikr-text,
.font-size-medium .dhikr-arabic,
.font-size-medium .dhikr-translation,
.font-size-medium .prayer-name,
.font-size-medium .prayer-time,
.font-size-medium .dhikr-count,
.font-size-medium .dhikr-source {
  font-size: 16px !important;
}

.font-size-large .dhikr-text,
.font-size-large .dhikr-arabic,
.font-size-large .dhikr-translation,
.font-size-large .prayer-name,
.font-size-large .prayer-time,
.font-size-large .dhikr-count,
.font-size-large .dhikr-source {
  font-size: 18px !important;
}

.font-size-extra-large .dhikr-text,
.font-size-extra-large .dhikr-arabic,
.font-size-extra-large .dhikr-translation,
.font-size-extra-large .prayer-name,
.font-size-extra-large .prayer-time,
.font-size-extra-large .dhikr-count,
.font-size-extra-large .dhikr-source {
  font-size: 22px !important;
}

/* الملف الشخصي */
.user-profile-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--color-border);
}

.user-profile-image {
  position: relative;
}

.header-profile-avatar,
.profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

#header-profile-image,
#profile-image-preview {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--color-primary);
}

.welcome-text {
  flex: 1;
}

.welcome-text h2 {
  margin: 0 0 8px 0;
  color: var(--color-primary);
}

.welcome-text p {
  margin: 0;
  color: var(--color-text-secondary);
}

.profile-section {
  margin: 20px 0;
  padding: 20px;
  background: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--color-border);
}

.profile-image-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 15px 0;
}

.current-profile-image {
  position: relative;
}

.profile-image-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.profile-image-controls button {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.avatar-selection {
  margin: 15px 0;
  padding: 15px;
  background: var(--color-background);
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

.avatar-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
  margin-top: 10px;
}

.avatar-option {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--color-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.avatar-option:hover {
  border-color: var(--color-primary);
  transform: scale(1.1);
}

.avatar-option.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

/* أوقات الصلاة */
.prayer-times-widget {
  margin: 20px 0;
  padding: 15px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
  border-radius: 12px;
  border-right: 4px solid var(--color-primary);
}

.dark-theme .prayer-times-widget {
  background: linear-gradient(135deg, #1a4a1a 0%, #2d5a2d 100%);
}

.prayer-times-widget h3 {
  margin-bottom: 15px;
  color: var(--color-primary);
  text-align: center;
}

.prayer-times-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.prayer-time-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.dark-theme .prayer-time-item {
  background: rgba(0, 0, 0, 0.3);
}

.prayer-time-item.next-prayer {
  background: var(--color-primary);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.prayer-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 5px;
}

.prayer-time {
  font-size: 14px;
  font-family: 'Courier New', monospace;
}

.next-prayer {
  text-align: center;
  padding: 10px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 8px;
  margin-bottom: 10px;
}

.dark-theme .next-prayer {
  background: rgba(76, 175, 80, 0.2);
}

.hijri-date {
  text-align: center;
  font-size: 14px;
  color: var(--color-text-secondary);
  font-style: italic;
}

.prayer-times-error {
  text-align: center;
  color: var(--color-error);
  padding: 20px;
  background: rgba(244, 67, 54, 0.1);
  border-radius: 8px;
  margin: 10px 0;
}

.location-permission {
  text-align: center;
  padding: 15px;
  background: rgba(255, 152, 0, 0.1);
  border-radius: 8px;
  margin: 10px 0;
}

.location-permission button {
  background: var(--color-warning);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.location-permission button:hover {
  background: #e68900;
}

/* تحسينات إضافية للإعدادات */
.settings-form h3 {
  margin: 25px 0 15px 0;
  color: var(--color-primary);
  border-bottom: 2px solid var(--color-border);
  padding-bottom: 8px;
}

.settings-form h3:first-child {
  margin-top: 0;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: var(--color-text);
}

.form-group input[type="time"],
.form-group input[type="text"],
.form-group input[type="number"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
}

.form-group input[type="checkbox"] {
  margin-left: 8px;
  transform: scale(1.2);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 450px) {
  .prayer-times-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .prayer-time-item {
    padding: 8px;
  }

  .prayer-name {
    font-size: 11px;
  }

  .prayer-time {
    font-size: 12px;
  }

  .theme-selector {
    grid-template-columns: repeat(2, 1fr);
  }

  .theme-option {
    padding: 10px;
  }
}

/* التقويم الهجري والمناسبات الإسلامية */
.hijri-month-info {
  margin: 15px 0;
  padding: 12px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 8px;
  border-right: 3px solid var(--color-primary);
}

.dark-theme .hijri-month-info {
  background: linear-gradient(135deg, #1a237e 0%, #4a148c 100%);
}

.progress-bar-hijri {
  width: 100%;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
  margin: 8px 0;
}

.progress-fill-hijri {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  transition: width 0.3s ease;
}

.islamic-events {
  margin: 15px 0;
  padding: 15px;
  background: linear-gradient(135deg, #fff3e0 0%, #ffecb3 100%);
  border-radius: 8px;
  border-right: 3px solid #FF9800;
}

.dark-theme .islamic-events {
  background: linear-gradient(135deg, #3e2723 0%, #5d4037 100%);
}

.events-list {
  max-height: 200px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.event-item:last-child {
  border-bottom: none;
}

.event-icon {
  font-size: 18px;
  margin-left: 10px;
}

.event-details {
  flex: 1;
}

.event-name {
  font-weight: bold;
  color: var(--color-text);
}

.event-date {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.event-importance-high {
  border-right: 3px solid #f44336;
}

.event-importance-medium {
  border-right: 3px solid #FF9800;
}

.event-importance-low {
  border-right: 3px solid #4CAF50;
}

/* أذكار رمضان */
.ramadan-header {
  text-align: center;
  margin-bottom: 20px;
}

.ramadan-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin: 15px 0;
}

.progress-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: conic-gradient(var(--color-primary) 0deg, #e0e0e0 0deg);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-text {
  font-size: 24px;
  font-weight: bold;
  color: var(--color-primary);
}

.progress-label {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.ramadan-details {
  text-align: right;
}

.special-night {
  color: #FF9800;
  font-weight: bold;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { text-shadow: 0 0 5px #FF9800; }
  to { text-shadow: 0 0 20px #FF9800, 0 0 30px #FF9800; }
}

.ramadan-tips {
  margin: 20px 0;
  padding: 15px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
  border-radius: 8px;
  border-right: 3px solid #4CAF50;
}

.tips-list {
  list-style: none;
  padding: 0;
}

.tip-item {
  padding: 5px 0;
  color: var(--color-text);
}

.ramadan-azkar-sections {
  margin: 20px 0;
}

.azkar-section {
  margin: 15px 0;
  padding: 15px;
  background: var(--color-surface);
  border-radius: 8px;
  border-right: 3px solid var(--color-primary);
}

.special-section {
  background: linear-gradient(135deg, #fff3e0 0%, #ffecb3 100%);
  border-right-color: #FF9800;
}

.dark-theme .special-section {
  background: linear-gradient(135deg, #3e2723 0%, #5d4037 100%);
}

/* الإنجازات */
.achievements-header {
  text-align: center;
  margin-bottom: 20px;
}

.user-level {
  margin: 20px 0;
}

.level-info {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 12px;
  border: 2px solid var(--color-primary);
}

.dark-theme .level-info {
  background: linear-gradient(135deg, #1a237e 0%, #4a148c 100%);
}

/* الوضع الليلي للملف الشخصي */
.dark-theme .user-profile-header {
  background: #2d2d2d;
  border-color: #404040;
}

.dark-theme .profile-section {
  background: #2d2d2d;
  border-color: #404040;
}

.dark-theme .avatar-selection {
  background: #1e1e1e;
  border-color: #404040;
}

.dark-theme .avatar-option {
  background: #2d2d2d;
  border-color: transparent;
}

.dark-theme .avatar-option:hover {
  border-color: #1976D2;
  background: #353535;
}

.dark-theme .avatar-option.selected {
  border-color: #1976D2;
  background: rgba(25, 118, 210, 0.2);
}

/* تسجيل الدخول بـ Google */
.google-auth-section {
  margin: 15px 0;
  padding: 15px;
  background: var(--color-surface);
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

.btn-google {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 12px 16px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-google:hover {
  background: #3367d6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
}

.google-icon {
  font-size: 18px;
}

.google-benefits {
  margin: 10px 0 0 0;
  font-size: 12px;
  color: var(--color-text-secondary);
  line-height: 1.4;
}

.google-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
  padding: 12px;
  background: var(--color-background);
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

.google-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--color-primary);
}

.google-user-details h4 {
  margin: 0 0 4px 0;
  color: var(--color-text);
  font-size: 16px;
}

.google-user-details p {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: 14px;
}

.google-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.google-actions button {
  flex: 1;
  min-width: 120px;
}

/* الوضع الليلي لـ Google Auth */
.dark-theme .google-auth-section {
  background: #2d2d2d;
  border-color: #404040;
}

.dark-theme .btn-google {
  background: #4285f4;
  color: white;
}

.dark-theme .btn-google:hover {
  background: #3367d6;
}

.dark-theme .google-user-info {
  background: #1e1e1e;
  border-color: #404040;
}

.dark-theme .google-user-details h4 {
  color: #ffffff;
}

.dark-theme .google-user-details p {
  color: #b0b0b0;
}

/* عداد الأذكار اليومي */
.daily-progress {
  margin: 15px 0;
  padding: 12px 15px;
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
  border-radius: 10px;
  border-right: 4px solid var(--color-primary);
}

.progress-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.progress-label {
  font-size: 14px;
  color: var(--color-text);
  font-weight: 500;
}

.progress-count {
  font-size: 18px;
  font-weight: bold;
  color: var(--color-primary);
  background: white;
  padding: 4px 12px;
  border-radius: 20px;
  min-width: 30px;
  text-align: center;
  transition: all 0.3s ease;
}

.progress-icon {
  font-size: 20px;
}

/* أزرار القراءة والمشاركة */
.button-container {
  display: flex;
  gap: 8px;
  margin-top: 10px;
  justify-content: flex-end;
}

.read-button, .share-button {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.read-button {
  background: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
}

.read-button:hover {
  background: #4CAF50;
  color: white;
  transform: translateY(-1px);
}

.share-button {
  background: #2196F3;
  color: white;
}

.share-button:hover {
  background: #1976D2;
  transform: translateY(-1px);
}

/* تأثيرات الذكر المقروء */
.read-zikr {
  background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
  border-right-color: #4CAF50;
  opacity: 0.8;
}

.read-zikr .zikr-text {
  color: #2e7d32;
}

/* إشعارات الإنجازات */
.achievement-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 15px 20px;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
  z-index: 1000;
  max-width: 300px;
  animation: slideInRight 0.5s ease-out;
  display: flex;
  align-items: center;
  gap: 15px;
}

.achievement-icon {
  font-size: 30px;
  animation: bounce 1s infinite;
}

.achievement-content h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: bold;
}

.achievement-content h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #e8f5e8;
}

.achievement-content p {
  margin: 0;
  font-size: 12px;
  opacity: 0.9;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* الوضع الليلي للعناصر الجديدة */
.dark-theme .daily-progress {
  background: linear-gradient(135deg, #1a4d1a 0%, #2d5a2d 100%);
  border-right-color: #4CAF50;
}

.dark-theme .progress-count {
  background: #2d2d2d;
  color: #4CAF50;
}

.dark-theme .read-button {
  background: #424242;
  color: #e0e0e0;
  border-color: #555;
}

.dark-theme .read-button:hover {
  background: #4CAF50;
  color: white;
}

.dark-theme .read-zikr {
  background: linear-gradient(135deg, #1a2e1a 0%, #2d4a2d 100%);
  border-right-color: #4CAF50;
}

.dark-theme .read-zikr .zikr-text {
  color: #81c784;
}

/* تعليمات إعداد Google */
.google-setup-instructions {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.setup-modal {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.setup-content {
  padding: 25px;
}

.setup-content h3 {
  margin: 0 0 15px 0;
  color: #333;
  text-align: center;
}

.setup-steps {
  margin: 20px 0;
  padding-right: 20px;
}

.setup-steps li {
  margin: 10px 0;
  line-height: 1.5;
}

.setup-steps a {
  color: #4285f4;
  text-decoration: none;
}

.setup-steps a:hover {
  text-decoration: underline;
}

.setup-note {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-right: 4px solid #ffc107;
  margin: 20px 0;
}

.setup-note p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.close-setup-btn {
  background: #4285f4;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: block;
  margin: 20px auto 0;
  transition: background 0.3s ease;
}

.close-setup-btn:hover {
  background: #3367d6;
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* الوضع الليلي لتعليمات الإعداد */
.dark-theme .setup-modal {
  background: #2d2d2d;
  color: #e0e0e0;
}

.dark-theme .setup-content h3 {
  color: #ffffff;
}

.dark-theme .setup-note {
  background: #3d3d3d;
  border-right-color: #ffc107;
}

.dark-theme .setup-note p {
  color: #b0b0b0;
}

/* الرسم البياني للتقدم */
.progress-chart-section {
  margin: 20px 0;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
}

.progress-chart-section h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
  text-align: center;
}

.chart-container {
  position: relative;
  width: 100%;
  height: 200px;
  background: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-chart {
  width: 100%;
  height: 100%;
}

/* تأثيرات الحركة المحسنة */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rainbow {
  0% { color: #ff6b6b; }
  16% { color: #ffa726; }
  33% { color: #ffeb3b; }
  50% { color: #66bb6a; }
  66% { color: #42a5f5; }
  83% { color: #ab47bc; }
  100% { color: #ff6b6b; }
}

/* تأثيرات خاصة للإنجازات */
.achievement-sparkle {
  animation: sparkle 0.6s ease-in-out;
}

.rainbow-text {
  animation: rainbow 2s linear infinite;
  font-weight: bold;
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

/* تحسينات للأزرار */
.read-button.completed {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

.share-button:active {
  transform: scale(0.95);
}

/* تأثيرات الجسيمات */
.particle-container {
  pointer-events: none;
  z-index: 999;
}

.reading-particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
}

.celebration-particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
}

/* تحسينات للعداد */
.progress-count.milestone {
  background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
  color: #333;
  animation: pulse 1s ease-in-out 3;
}

/* الوضع الليلي للرسم البياني */
.dark-theme .progress-chart-section {
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  border-color: #4a5568;
}

.dark-theme .progress-chart-section h4 {
  color: #e2e8f0;
}

.dark-theme .chart-container {
  background: #1a202c;
  border: 1px solid #4a5568;
}

/* تحسينات إضافية للتفاعل */
.zikr-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.button-container button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* تأثيرات التحميل */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* تحسينات للإشعارات */
.success-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
  z-index: 1500;
  animation: slideInRight 0.3s ease-out;
  max-width: 300px;
}

/* تأثيرات الهوفر المحسنة */
.daily-progress:hover {
  transform: scale(1.02);
  transition: transform 0.3s ease;
}

.progress-chart-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.level-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.level-details {
  flex: 1;
}

.level-details h3 {
  margin: 0 0 8px 0;
  color: var(--color-primary);
}

.level-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.level-progress .progress-bar {
  flex: 1;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.level-progress .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: var(--color-text-secondary);
  white-space: nowrap;
}

.stats-overview {
  margin: 20px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  margin: 15px 0;
}

.stat-card {
  text-align: center;
  padding: 15px;
  background: var(--color-surface);
  border-radius: 8px;
  border: 1px solid var(--color-border);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: var(--color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.active-challenges {
  margin: 20px 0;
}

.challenges-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.challenge-item {
  padding: 15px;
  background: var(--color-surface);
  border-radius: 8px;
  border-right: 3px solid var(--color-secondary);
}

.challenge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.challenge-name {
  font-weight: bold;
  color: var(--color-text);
}

.challenge-points {
  font-size: 12px;
  color: var(--color-secondary);
  background: rgba(33, 150, 243, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.challenge-description {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 8px;
}

.challenge-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.challenge-progress .progress-bar {
  flex: 1;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
}

.challenge-progress .progress-fill {
  height: 100%;
  background: var(--color-secondary);
  transition: width 0.3s ease;
}

.challenge-progress-text {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.achievements-gallery {
  margin: 20px 0;
}

.achievements-filter {
  display: flex;
  gap: 10px;
  margin: 15px 0;
  justify-content: center;
}

.filter-btn {
  padding: 8px 16px;
  border: 1px solid var(--color-border);
  background: var(--color-surface);
  color: var(--color-text);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: var(--color-primary-light);
}

.filter-btn.active {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.achievement-card {
  padding: 15px;
  background: var(--color-surface);
  border-radius: 8px;
  border: 1px solid var(--color-border);
  text-align: center;
  transition: all 0.3s ease;
}

.achievement-card.unlocked {
  border-color: var(--color-primary);
  background: linear-gradient(135deg, var(--color-surface) 0%, rgba(76, 175, 80, 0.1) 100%);
}

.achievement-card.locked {
  opacity: 0.6;
  filter: grayscale(50%);
}

.achievement-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.achievement-name {
  font-weight: bold;
  color: var(--color-text);
  margin-bottom: 5px;
}

.achievement-description {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-bottom: 8px;
}

.achievement-points {
  font-size: 14px;
  color: var(--color-primary);
  font-weight: bold;
}

.recent-achievements {
  margin: 20px 0;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.recent-achievement {
  display: flex;
  align-items: center;
  padding: 10px;
  background: var(--color-surface);
  border-radius: 8px;
  border-right: 3px solid var(--color-primary);
}

.recent-achievement .achievement-icon {
  font-size: 24px;
  margin-left: 10px;
  margin-bottom: 0;
}

.recent-achievement-details {
  flex: 1;
}

.recent-achievement-name {
  font-weight: bold;
  color: var(--color-text);
}

.recent-achievement-points {
  font-size: 12px;
  color: var(--color-primary);
}

.no-achievements {
  text-align: center;
  color: var(--color-text-secondary);
  font-style: italic;
  padding: 20px;
}

/* رسائل الخطأ والنجاح */
.audio-error-message {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #4CAF50;
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  z-index: 1000;
  font-size: 14px;
  max-width: 300px;
  animation: slideInRight 0.3s ease-out;
}

/* تحسينات بصرية إضافية */
.zikr-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.counter-display:hover {
  background: var(--color-primary-light);
  transform: scale(1.05);
}

.tab:hover {
  background: rgba(255,255,255,0.1);
}

.btn:active {
  transform: translateY(1px);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 450px) {
  .ramadan-progress {
    flex-direction: column;
    gap: 10px;
  }

  .level-info {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .achievements-grid {
    grid-template-columns: 1fr;
  }

  .achievements-filter {
    flex-wrap: wrap;
  }

  .container {
    padding: 10px;
  }

  .zikr-item {
    padding: 12px;
  }

  .tab {
    padding: 8px 12px;
    font-size: 13px;
  }
}

/* نظام المشاركة */
.share-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.share-dialog-content {
  background: var(--color-background);
  border-radius: 12px;
  padding: 20px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

.share-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--color-border);
}

.share-dialog-header h3 {
  margin: 0;
  color: var(--color-text);
}

.close-dialog {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-dialog:hover {
  background: var(--color-border);
  color: var(--color-text);
}

.share-preview {
  margin-bottom: 20px;
  padding: 15px;
  background: var(--color-card);
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

.zikr-preview .zikr-text {
  font-size: 18px;
  line-height: 1.6;
  color: var(--color-text);
  margin-bottom: 10px;
  text-align: center;
}

.zikr-preview .zikr-translation {
  font-size: 14px;
  color: var(--color-text-secondary);
  font-style: italic;
  text-align: center;
  margin: 0;
}

.share-options h4 {
  color: var(--color-text);
  margin-bottom: 15px;
  font-size: 16px;
}

.share-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
}

.share-btn {
  padding: 12px 16px;
  border: 2px solid var(--color-primary);
  background: transparent;
  color: var(--color-primary);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.share-btn:hover {
  background: var(--color-primary);
  color: white;
  transform: translateY(-1px);
}

.template-selection {
  margin-bottom: 20px;
}

.template-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
}

.template-btn {
  padding: 10px 12px;
  border: 1px solid var(--color-border);
  background: var(--color-card);
  color: var(--color-text);
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
}

.template-btn:hover {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.image-preview {
  text-align: center;
}

.preview-canvas {
  max-width: 100%;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  margin-bottom: 15px;
}

.image-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.image-actions button {
  padding: 8px 16px;
  border: 1px solid var(--color-primary);
  background: var(--color-primary);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
}

.image-actions button:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
}

.share-button {
  background: none;
  border: none;
  color: var(--color-primary);
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin-left: 8px;
}

.share-button:hover {
  background: var(--color-primary-light);
  transform: scale(1.1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 450px) {
  .share-dialog-content {
    width: 95%;
    padding: 15px;
  }

  .share-buttons {
    grid-template-columns: 1fr;
  }

  .template-buttons {
    grid-template-columns: repeat(2, 1fr);
  }

  .image-actions {
    flex-direction: column;
  }
}

/* نظام النسخ الاحتياطي */
.backup-interface {
  margin-top: 20px;
}

.backup-section h3 {
  color: var(--color-text);
  margin-bottom: 20px;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.backup-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 25px;
}

.backup-option {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 15px;
  transition: all 0.2s ease;
}

.backup-option:hover {
  border-color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.backup-option-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
}

.backup-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-primary-light);
  border-radius: 8px;
}

.backup-info h4 {
  margin: 0 0 5px 0;
  color: var(--color-text);
  font-size: 16px;
}

.backup-info p {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.backup-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.backup-actions .btn {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.backup-btn {
  background: var(--color-primary);
  color: white;
}

.backup-btn:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
}

.restore-btn {
  background: var(--color-secondary);
  color: white;
}

.restore-btn:hover {
  background: var(--color-secondary-dark);
  transform: translateY(-1px);
}

.sync-btn {
  background: #2196F3;
  color: white;
}

.sync-btn:hover {
  background: #1976D2;
  transform: translateY(-1px);
}

.auto-backup-btn {
  background: #FF9800;
  color: white;
}

.auto-backup-btn:hover {
  background: #F57C00;
  transform: translateY(-1px);
}

.backup-info-section {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 15px;
}

.backup-info-section h4 {
  margin: 0 0 15px 0;
  color: var(--color-text);
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.backup-stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.backup-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--color-border);
}

.backup-stat:last-child {
  border-bottom: none;
}

.stat-label {
  color: var(--color-text-secondary);
  font-size: 14px;
}

.stat-value {
  color: var(--color-text);
  font-size: 14px;
  font-weight: 500;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 450px) {
  .backup-option-header {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .backup-actions {
    justify-content: center;
  }

  .backup-actions .btn {
    flex: 1;
    min-width: 120px;
  }

  .backup-stats {
    gap: 8px;
  }

  .backup-stat {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
